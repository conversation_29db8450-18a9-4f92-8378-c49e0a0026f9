# 线上协作与创业撮合平台 Demo

一个基于科学性格分析和智能匹配算法的线上合作平台，帮助用户找到最适合的项目团队和合作伙伴。

## 🌟 项目特色

- **科学匹配算法**：基于MBTI性格测试和六维能力评估
- **完整用户体验**：从信息收集到匹配推荐的完整流程
- **现代化设计**：采用Modern Balanced色彩策略，优雅且专业
- **响应式布局**：完美适配桌面端和移动端

## 🚀 技术栈

- **前端框架**：Next.js 14 + TypeScript
- **样式方案**：Tailwind CSS + 自定义CSS变量
- **UI组件**：Radix UI + 自定义组件
- **图表库**：Recharts（六边形雷达图）
- **表单处理**：React Hook Form + Zod验证
- **图标库**：Lucide React
- **部署平台**：Cloudflare Pages

## 📱 功能模块

### 1. 首页 (/)
- 项目介绍和价值展示
- 成功案例展示
- 引导用户开始体验

### 2. 体验流程 (/experience)
- **8步完整注册流程**：
  1. 基本信息收集
  2. 技能资源评估
  3. MBTI性格测试（3种方式）
  4. 合作偏好设置
  5. 时间投入与动机
  6. 个性标签选择
  7. 六维能力自评
  8. 信息确认与提交

### 3. 个人画像 (/profile)
- 完整的个人合作画像展示
- 六维能力雷达图可视化
- MBTI性格类型展示
- 技能标签和个性特质
- 合作偏好总览

### 4. 智能匹配 (/match)
- **合作伙伴推荐**：基于技能互补和性格匹配
- **项目推荐**：根据兴趣和能力匹配
- 匹配度评分和原因说明
- 互动功能（聊天、收藏）

## 🎨 设计系统

### 色彩方案
- **主色调**：珊瑚粉 (#FF6B6B)
- **次要色**：薄荷绿 (#1ABC9C)
- **强调色**：品牌紫 (#8E44AD)
- **中性色**：柔灰 (#BDC3C7)
- **背景色**：雾白 (#ECF0F1)

### 设计原则
- 大卡片模型，明确分区
- 渐变背景，视觉层次清晰
- 悬停动效，提升交互体验
- 响应式设计，多端适配

## 🛠️ 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📊 核心功能

### MBTI测试系统
- 支持3种获取方式：已知结果、第三方测试、快速测试
- 快速测试包含4道核心问题
- 自动计算MBTI类型结果

### 六维能力评估
- 交互式滑块评分（1-10分）
- 实时雷达图可视化
- 综合评分计算

### 智能匹配算法
- 基于技能互补度计算
- 性格类型兼容性分析
- 工作方式相似度评估
- 综合匹配度评分

## 🚀 部署到Cloudflare

项目已配置为支持Cloudflare Pages部署：

1. 连接GitHub仓库到Cloudflare Pages
2. 设置构建命令：`npm run build`
3. 设置输出目录：`.next`
4. 自动部署完成

## 📝 开发状态

### 已完成 ✅
- [x] 项目基础架构搭建
- [x] 首页设计和实现
- [x] 完整的8步注册流程
- [x] 个人画像展示页面
- [x] 智能匹配推荐页面
- [x] 响应式设计适配
- [x] 模拟数据和交互

### 下一步计划 🔄
- [ ] 后端API集成
- [ ] 用户认证系统
- [ ] 实时聊天功能
- [ ] 项目创建和管理
- [ ] 评价和反馈系统

---

**注意**：这是一个演示版本，专注于UI/UX展示。实际的匹配算法和后端功能需要在后续开发中完善。
