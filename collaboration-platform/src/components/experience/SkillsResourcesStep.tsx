'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { PRESET_TAGS } from '@/data/mock-data'
import { Plus, X } from 'lucide-react'

interface SkillsResourcesStepProps {
  data: any
  onDataChange: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

export default function SkillsResourcesStep({ data, onDataChange, onNext }: SkillsResourcesStepProps) {
  const [formData, setFormData] = useState({
    skills: data.skills || [],
    resources: data.resources || [],
    ideas: data.ideas || '',
    funding: data.funding || '',
    customSkill: '',
    customResource: ''
  })

  useEffect(() => {
    onDataChange(formData)
  }, [formData, onDataChange])

  const handleSkillToggle = (skill: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.includes(skill)
        ? prev.skills.filter((s: string) => s !== skill)
        : [...prev.skills, skill]
    }))
  }

  const handleAddCustomSkill = () => {
    if (formData.customSkill.trim() && !formData.skills.includes(formData.customSkill.trim())) {
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, prev.customSkill.trim()],
        customSkill: ''
      }))
    }
  }

  const handleResourceChange = (type: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      resources: {
        ...prev.resources,
        [type]: value
      }
    }))
  }

  const removeSkill = (skill: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter((s: string) => s !== skill)
    }))
  }

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">
          技能与资源
        </h2>
        <p className="text-[var(--soft-gray)]">
          告诉我们你拥有的技能和可以提供的资源
        </p>
      </div>

      {/* 技能选择 */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          你的技能专长
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mb-4">
          {PRESET_TAGS.skills.map((skill) => (
            <button
              key={skill}
              onClick={() => handleSkillToggle(skill)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                formData.skills.includes(skill)
                  ? 'bg-[var(--coral-pink)] text-white'
                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white'
              }`}
            >
              {skill}
            </button>
          ))}
        </div>

        {/* 自定义技能 */}
        <div className="flex gap-2">
          <input
            type="text"
            value={formData.customSkill}
            onChange={(e) => setFormData(prev => ({ ...prev, customSkill: e.target.value }))}
            placeholder="添加其他技能..."
            className="flex-1 px-4 py-2 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent"
            onKeyPress={(e) => e.key === 'Enter' && handleAddCustomSkill()}
          />
          <Button onClick={handleAddCustomSkill} variant="outline" size="sm">
            <Plus className="w-4 h-4" />
          </Button>
        </div>

        {/* 已选技能 */}
        {formData.skills.length > 0 && (
          <div className="mt-4">
            <p className="text-sm text-[var(--soft-gray)] mb-2">已选择的技能：</p>
            <div className="flex flex-wrap gap-2">
              {formData.skills.map((skill: string) => (
                <span
                  key={skill}
                  className="inline-flex items-center px-3 py-1 bg-[var(--coral-pink)] text-white text-sm rounded-full"
                >
                  {skill}
                  <button
                    onClick={() => removeSkill(skill)}
                    className="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 资源类型 */}
      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
            创意想法
          </label>
          <textarea
            value={formData.ideas}
            onChange={(e) => setFormData(prev => ({ ...prev, ideas: e.target.value }))}
            rows={4}
            className="w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent"
            placeholder="描述你的项目想法或创意概念..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
            资金情况
          </label>
          <select
            value={formData.funding}
            onChange={(e) => setFormData(prev => ({ ...prev, funding: e.target.value }))}
            className="w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent"
          >
            <option value="">选择资金情况</option>
            <option value="none">暂无资金</option>
            <option value="small">少量资金（1-5万）</option>
            <option value="medium">中等资金（5-20万）</option>
            <option value="large">充足资金（20万以上）</option>
            <option value="seeking">正在寻求投资</option>
          </select>
        </div>
      </div>

      {/* 其他资源 */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          其他可提供的资源
        </h3>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
              人脉网络
            </label>
            <input
              type="text"
              value={formData.resources.network || ''}
              onChange={(e) => handleResourceChange('network', e.target.value)}
              className="w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent"
              placeholder="如：投资人、行业专家、技术大牛等"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
              设备工具
            </label>
            <input
              type="text"
              value={formData.resources.equipment || ''}
              onChange={(e) => handleResourceChange('equipment', e.target.value)}
              className="w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent"
              placeholder="如：服务器、设计软件、办公场地等"
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end mt-8">
        <Button onClick={onNext} size="lg">
          下一步：MBTI测试
        </Button>
      </div>
    </div>
  )
}
