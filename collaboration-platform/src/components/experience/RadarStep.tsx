'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { RADAR_DIMENSIONS } from '@/data/mock-data'
import { Radar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, ResponsiveContainer } from 'recharts'

interface RadarStepProps {
  data: any
  onDataChange: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

export default function RadarStep({ data, onDataChange, onNext }: RadarStepProps) {
  const [formData, setFormData] = useState({
    radarData: data.radarData || RADAR_DIMENSIONS.reduce((acc, dim) => ({
      ...acc,
      [dim.key]: 5
    }), {})
  })

  useEffect(() => {
    onDataChange(formData)
  }, [formData, onDataChange])

  const handleScoreChange = (dimension: string, score: number) => {
    setFormData(prev => ({
      ...prev,
      radarData: {
        ...prev.radarData,
        [dimension]: score
      }
    }))
  }

  const radarChartData = RADAR_DIMENSIONS.map(dim => ({
    dimension: dim.label,
    value: formData.radarData[dim.key] || 5,
    fullMark: 10
  }))

  const getScoreDescription = (score: number) => {
    if (score <= 3) return '初级'
    if (score <= 6) return '中级'
    if (score <= 8) return '高级'
    return '专家'
  }

  const getScoreColor = (score: number) => {
    if (score <= 3) return '#ff6b6b'
    if (score <= 6) return '#ffa726'
    if (score <= 8) return '#66bb6a'
    return '#42a5f5'
  }

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">
          六维能力评估
        </h2>
        <p className="text-[var(--soft-gray)]">
          诚实评估你在各个维度的能力水平
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* 能力评分 */}
        <div className="space-y-6">
          {RADAR_DIMENSIONS.map((dimension) => (
            <div key={dimension.key} className="bg-[var(--mist-white)] p-4 rounded-lg">
              <div className="flex justify-between items-center mb-3">
                <div>
                  <h3 className="font-semibold text-[var(--dark-blue-gray)]">
                    {dimension.label}
                  </h3>
                  <p className="text-sm text-[var(--soft-gray)]">
                    {dimension.description}
                  </p>
                </div>
                <div className="text-right">
                  <div 
                    className="text-2xl font-bold"
                    style={{ color: getScoreColor(formData.radarData[dimension.key]) }}
                  >
                    {formData.radarData[dimension.key]}
                  </div>
                  <div className="text-xs text-[var(--soft-gray)]">
                    {getScoreDescription(formData.radarData[dimension.key])}
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <input
                  type="range"
                  min="1"
                  max="10"
                  value={formData.radarData[dimension.key]}
                  onChange={(e) => handleScoreChange(dimension.key, parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  style={{
                    background: `linear-gradient(to right, ${getScoreColor(formData.radarData[dimension.key])} 0%, ${getScoreColor(formData.radarData[dimension.key])} ${formData.radarData[dimension.key] * 10}%, #e5e7eb ${formData.radarData[dimension.key] * 10}%, #e5e7eb 100%)`
                  }}
                />
                <div className="flex justify-between text-xs text-[var(--soft-gray)]">
                  <span>1 (初学)</span>
                  <span>5 (中等)</span>
                  <span>10 (专家)</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 雷达图 */}
        <div className="bg-white p-6 rounded-lg card-shadow">
          <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4 text-center">
            能力雷达图
          </h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RadarChart data={radarChartData}>
                <PolarGrid />
                <PolarAngleAxis 
                  dataKey="dimension" 
                  tick={{ fontSize: 12, fill: '#7F8C8D' }}
                />
                <PolarRadiusAxis 
                  angle={90} 
                  domain={[0, 10]} 
                  tick={{ fontSize: 10, fill: '#BDC3C7' }}
                />
                <Radar
                  name="能力值"
                  dataKey="value"
                  stroke="#FF6B6B"
                  fill="#FF6B6B"
                  fillOpacity={0.3}
                  strokeWidth={2}
                />
              </RadarChart>
            </ResponsiveContainer>
          </div>
          
          {/* 总体评分 */}
          <div className="mt-4 text-center">
            <div className="text-sm text-[var(--soft-gray)] mb-2">综合能力评分</div>
            <div className="text-2xl font-bold text-[var(--coral-pink)]">
              {(Object.values(formData.radarData).reduce((sum: number, score: any) => sum + score, 0) / 6).toFixed(1)}
            </div>
            <div className="text-xs text-[var(--soft-gray)]">
              / 10.0
            </div>
          </div>
        </div>
      </div>

      {/* 评分指南 */}
      <div className="bg-[var(--mist-white)] p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          📊 评分参考指南
        </h3>
        <div className="grid md:grid-cols-2 gap-6 text-sm">
          <div>
            <h4 className="font-semibold text-[var(--dark-blue-gray)] mb-2">评分标准：</h4>
            <ul className="space-y-1 text-[var(--soft-gray)]">
              <li><strong>1-3分：</strong> 初学者水平，需要学习和提升</li>
              <li><strong>4-6分：</strong> 中等水平，有一定经验</li>
              <li><strong>7-8分：</strong> 高级水平，经验丰富</li>
              <li><strong>9-10分：</strong> 专家水平，可以指导他人</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-[var(--dark-blue-gray)] mb-2">诚实评估的重要性：</h4>
            <ul className="space-y-1 text-[var(--soft-gray)]">
              <li>• 帮助找到互补的合作伙伴</li>
              <li>• 避免能力不匹配的问题</li>
              <li>• 建立真实可信的合作关系</li>
              <li>• 促进团队协作效率</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-end mt-8">
        <Button onClick={onNext} size="lg">
          下一步：信息确认
        </Button>
      </div>
    </div>
  )
}
