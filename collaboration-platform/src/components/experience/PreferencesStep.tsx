'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { PRESET_TAGS } from '@/data/mock-data'

interface PreferencesStepProps {
  data: any
  onDataChange: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

export default function PreferencesStep({ data, onDataChange, onNext }: PreferencesStepProps) {
  const [formData, setFormData] = useState({
    workStyle: data.workStyle || [],
    teamSize: data.teamSize || '',
    communicationStyle: data.communicationStyle || '',
    projectType: data.projectType || [],
    avoidTypes: data.avoidTypes || [],
    idealPartner: data.idealPartner || ''
  })

  useEffect(() => {
    onDataChange(formData)
  }, [formData, onDataChange])

  const handleWorkStyleToggle = (style: string) => {
    setFormData(prev => ({
      ...prev,
      workStyle: prev.workStyle.includes(style)
        ? prev.workStyle.filter((s: string) => s !== style)
        : [...prev.workStyle, style]
    }))
  }

  const handleProjectTypeToggle = (type: string) => {
    setFormData(prev => ({
      ...prev,
      projectType: prev.projectType.includes(type)
        ? prev.projectType.filter((t: string) => t !== type)
        : [...prev.projectType, type]
    }))
  }

  const handleAvoidTypeToggle = (type: string) => {
    setFormData(prev => ({
      ...prev,
      avoidTypes: prev.avoidTypes.includes(type)
        ? prev.avoidTypes.filter((t: string) => t !== type)
        : [...prev.avoidTypes, type]
    }))
  }

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">
          合作偏好
        </h2>
        <p className="text-[var(--soft-gray)]">
          告诉我们你喜欢的合作方式和项目类型
        </p>
      </div>

      {/* 工作方式偏好 */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          工作方式偏好
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {PRESET_TAGS.workStyle.map((style) => (
            <button
              key={style}
              onClick={() => handleWorkStyleToggle(style)}
              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all ${
                formData.workStyle.includes(style)
                  ? 'bg-[var(--mint-green)] text-white'
                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--mint-green)] hover:text-white'
              }`}
            >
              {style}
            </button>
          ))}
        </div>
      </div>

      {/* 团队规模偏好 */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          理想团队规模
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {['2-3人小团队', '4-6人中团队', '7-10人大团队', '10人以上'].map((size) => (
            <button
              key={size}
              onClick={() => setFormData(prev => ({ ...prev, teamSize: size }))}
              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all ${
                formData.teamSize === size
                  ? 'bg-[var(--coral-pink)] text-white'
                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white'
              }`}
            >
              {size}
            </button>
          ))}
        </div>
      </div>

      {/* 沟通方式 */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          偏好的沟通方式
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {[
            '面对面深度讨论',
            '在线视频会议',
            '文字消息沟通',
            '定期汇报总结',
            '随时随地交流',
            '结构化会议'
          ].map((style) => (
            <button
              key={style}
              onClick={() => setFormData(prev => ({ ...prev, communicationStyle: style }))}
              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all text-left ${
                formData.communicationStyle === style
                  ? 'bg-[var(--brand-purple)] text-white'
                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--brand-purple)] hover:text-white'
              }`}
            >
              {style}
            </button>
          ))}
        </div>
      </div>

      {/* 感兴趣的项目类型 */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          感兴趣的项目类型
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {PRESET_TAGS.interests.map((interest) => (
            <button
              key={interest}
              onClick={() => handleProjectTypeToggle(interest)}
              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all ${
                formData.projectType.includes(interest)
                  ? 'bg-[var(--ice-blue)] text-white'
                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--ice-blue)] hover:text-white'
              }`}
            >
              {interest}
            </button>
          ))}
        </div>
      </div>

      {/* 不喜欢的合作类型 */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          不太适合的合作类型（可选）
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {[
            '高压力快节奏',
            '需要频繁出差',
            '长期加班项目',
            '高风险投资',
            '纯技术导向',
            '纯商业导向'
          ].map((type) => (
            <button
              key={type}
              onClick={() => handleAvoidTypeToggle(type)}
              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all text-left ${
                formData.avoidTypes.includes(type)
                  ? 'bg-red-500 text-white'
                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-red-500 hover:text-white'
              }`}
            >
              {type}
            </button>
          ))}
        </div>
      </div>

      {/* 理想合作伙伴描述 */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          理想合作伙伴描述（可选）
        </h3>
        <textarea
          value={formData.idealPartner}
          onChange={(e) => setFormData(prev => ({ ...prev, idealPartner: e.target.value }))}
          rows={4}
          className="w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent"
          placeholder="描述你理想中的合作伙伴特质、工作风格等..."
        />
      </div>

      <div className="flex justify-end mt-8">
        <Button onClick={onNext} size="lg">
          下一步：时间与动机
        </Button>
      </div>
    </div>
  )
}
