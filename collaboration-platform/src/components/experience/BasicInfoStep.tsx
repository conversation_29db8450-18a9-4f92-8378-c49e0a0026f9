'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

interface BasicInfoStepProps {
  data: any
  onDataChange: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

export default function BasicInfoStep({ data, onDataChange, onNext }: BasicInfoStepProps) {
  const [formData, setFormData] = useState({
    name: data.name || '',
    age: data.age || '',
    gender: data.gender || '',
    location: data.location || '',
    email: data.email || '',
    telegram: data.telegram || ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    onDataChange(formData)
  }, [formData, onDataChange])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = '请输入昵称'
    }
    
    if (!formData.age) {
      newErrors.age = '请选择年龄段'
    }
    
    if (!formData.gender) {
      newErrors.gender = '请选择性别'
    }
    
    if (!formData.location.trim()) {
      newErrors.location = '请输入所在地区'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateForm()) {
      onNext()
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">
          基本信息
        </h2>
        <p className="text-[var(--soft-gray)]">
          让我们先了解一下你的基本情况
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* 昵称 */}
        <div>
          <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
            昵称 *
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent ${
              errors.name ? 'border-red-500' : 'border-[var(--soft-gray)]'
            }`}
            placeholder="请输入你的昵称"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-500">{errors.name}</p>
          )}
        </div>

        {/* 年龄段 */}
        <div>
          <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
            年龄段 *
          </label>
          <select
            value={formData.age}
            onChange={(e) => handleInputChange('age', e.target.value)}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent ${
              errors.age ? 'border-red-500' : 'border-[var(--soft-gray)]'
            }`}
          >
            <option value="">请选择年龄段</option>
            <option value="18-22">18-22岁</option>
            <option value="23-27">23-27岁</option>
            <option value="28-32">28-32岁</option>
            <option value="33-37">33-37岁</option>
            <option value="38-42">38-42岁</option>
            <option value="43+">43岁以上</option>
          </select>
          {errors.age && (
            <p className="mt-1 text-sm text-red-500">{errors.age}</p>
          )}
        </div>

        {/* 性别 */}
        <div>
          <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
            性别 *
          </label>
          <div className="flex space-x-4">
            {['男', '女', '其他'].map((option) => (
              <label key={option} className="flex items-center">
                <input
                  type="radio"
                  name="gender"
                  value={option}
                  checked={formData.gender === option}
                  onChange={(e) => handleInputChange('gender', e.target.value)}
                  className="mr-2 text-[var(--coral-pink)] focus:ring-[var(--coral-pink)]"
                />
                <span className="text-[var(--dark-blue-gray)]">{option}</span>
              </label>
            ))}
          </div>
          {errors.gender && (
            <p className="mt-1 text-sm text-red-500">{errors.gender}</p>
          )}
        </div>

        {/* 所在地区 */}
        <div>
          <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
            所在地区 *
          </label>
          <input
            type="text"
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent ${
              errors.location ? 'border-red-500' : 'border-[var(--soft-gray)]'
            }`}
            placeholder="如：北京、上海、深圳"
          />
          {errors.location && (
            <p className="mt-1 text-sm text-red-500">{errors.location}</p>
          )}
        </div>

        {/* 邮箱邮箱（可选） */}
        <div>
          <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
            邮箱邮箱（可选）
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className="w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>

        {/* Telegram联系方式（可选） */}
        <div>
          <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
            Telegram联系方式（可选）
          </label>
          <input
            type="text"
            value={formData.telegram}
            onChange={(e) => handleInputChange('telegram', e.target.value)}
            className="w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent"
            placeholder="@username 或 Telegram用户名"
          />
        </div>
      </div>

      <div className="flex justify-end mt-8">
        <Button onClick={handleNext} size="lg">
          下一步：技能资源
        </Button>
      </div>
    </div>
  )
}
