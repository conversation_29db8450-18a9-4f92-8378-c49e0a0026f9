'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { MBTI_TYPES } from '@/data/mock-data'
import { ExternalLink, Brain, CheckCircle } from 'lucide-react'

interface MBTIStepProps {
  data: any
  onDataChange: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

export default function MBTIStep({ data, onDataChange, onNext }: MBTIStepProps) {
  const [formData, setFormData] = useState({
    mbtiType: data.mbtiType || '',
    mbtiSource: data.mbtiSource || '',
    quickTestAnswers: data.quickTestAnswers || {}
  })

  const [showQuickTest, setShowQuickTest] = useState(false)
  const [currentQuestion, setCurrentQuestion] = useState(0)

  useEffect(() => {
    onDataChange(formData)
  }, [formData, onDataChange])

  const quickTestQuestions = [
    {
      question: "在聚会中，你更倾向于：",
      options: [
        { text: "与少数几个人深入交谈", type: "I" },
        { text: "与很多人轻松聊天", type: "E" }
      ]
    },
    {
      question: "做决定时，你更依赖：",
      options: [
        { text: "逻辑分析和客观事实", type: "T" },
        { text: "个人价值观和他人感受", type: "F" }
      ]
    },
    {
      question: "你更喜欢：",
      options: [
        { text: "制定详细计划并按计划执行", type: "J" },
        { text: "保持灵活性，随机应变", type: "P" }
      ]
    },
    {
      question: "学习新知识时，你更关注：",
      options: [
        { text: "具体的事实和细节", type: "S" },
        { text: "整体概念和可能性", type: "N" }
      ]
    }
  ]

  const handleMBTISelect = (mbtiCode: string) => {
    setFormData(prev => ({ 
      ...prev, 
      mbtiType: mbtiCode,
      mbtiSource: 'direct'
    }))
  }

  const handleQuickTestAnswer = (answer: string, type: string) => {
    const newAnswers = {
      ...formData.quickTestAnswers,
      [currentQuestion]: { answer, type }
    }
    
    setFormData(prev => ({
      ...prev,
      quickTestAnswers: newAnswers
    }))

    if (currentQuestion < quickTestQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
    } else {
      // 计算MBTI结果
      const types = { E: 0, I: 0, S: 0, N: 0, T: 0, F: 0, J: 0, P: 0 }
      Object.values(newAnswers).forEach((ans: any) => {
        types[ans.type as keyof typeof types]++
      })
      
      const result = 
        (types.E > types.I ? 'E' : 'I') +
        (types.S > types.N ? 'S' : 'N') +
        (types.T > types.F ? 'T' : 'F') +
        (types.J > types.P ? 'J' : 'P')
      
      setFormData(prev => ({
        ...prev,
        mbtiType: result,
        mbtiSource: 'quickTest'
      }))
      setShowQuickTest(false)
      setCurrentQuestion(0)
    }
  }

  const startQuickTest = () => {
    setShowQuickTest(true)
    setCurrentQuestion(0)
    setFormData(prev => ({ ...prev, quickTestAnswers: {} }))
  }

  const selectedMBTI = MBTI_TYPES.find(type => type.code === formData.mbtiType)

  if (showQuickTest) {
    const question = quickTestQuestions[currentQuestion]
    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">
            快速MBTI测试
          </h2>
          <p className="text-[var(--soft-gray)]">
            问题 {currentQuestion + 1} / {quickTestQuestions.length}
          </p>
        </div>

        <div className="max-w-2xl mx-auto">
          <div className="bg-[var(--mist-white)] p-6 rounded-lg mb-6">
            <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
              {question.question}
            </h3>
            <div className="space-y-3">
              {question.options.map((option, index) => (
                <button
                  key={index}
                  onClick={() => handleQuickTestAnswer(option.text, option.type)}
                  className="w-full p-4 text-left bg-white rounded-lg border border-[var(--soft-gray)] hover:border-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white transition-all"
                >
                  {option.text}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">
          MBTI性格类型
        </h2>
        <p className="text-[var(--soft-gray)]">
          选择获取MBTI结果的方式
        </p>
      </div>

      {/* 选择方式 */}
      <div className="grid md:grid-cols-3 gap-6">
        <div className="bg-[var(--mist-white)] p-6 rounded-lg text-center">
          <Brain className="w-12 h-12 text-[var(--coral-pink)] mx-auto mb-4" />
          <h3 className="font-semibold text-[var(--dark-blue-gray)] mb-2">
            我已知道结果
          </h3>
          <p className="text-sm text-[var(--soft-gray)] mb-4">
            直接选择你的MBTI类型
          </p>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setFormData(prev => ({ ...prev, mbtiSource: 'direct' }))}
          >
            直接选择
          </Button>
        </div>

        <div className="bg-[var(--mist-white)] p-6 rounded-lg text-center">
          <ExternalLink className="w-12 h-12 text-[var(--mint-green)] mx-auto mb-4" />
          <h3 className="font-semibold text-[var(--dark-blue-gray)] mb-2">
            专业测试
          </h3>
          <p className="text-sm text-[var(--soft-gray)] mb-4">
            跳转到专业MBTI测试网站
          </p>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => window.open('https://www.16personalities.com/ch', '_blank')}
          >
            去测试
          </Button>
        </div>

        <div className="bg-[var(--mist-white)] p-6 rounded-lg text-center">
          <CheckCircle className="w-12 h-12 text-[var(--brand-purple)] mx-auto mb-4" />
          <h3 className="font-semibold text-[var(--dark-blue-gray)] mb-2">
            快速测试
          </h3>
          <p className="text-sm text-[var(--soft-gray)] mb-4">
            4道题快速了解你的类型
          </p>
          <Button 
            variant="outline" 
            size="sm"
            onClick={startQuickTest}
          >
            开始测试
          </Button>
        </div>
      </div>

      {/* MBTI类型选择 */}
      {formData.mbtiSource === 'direct' && (
        <div>
          <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
            选择你的MBTI类型
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {MBTI_TYPES.map((type) => (
              <button
                key={type.code}
                onClick={() => handleMBTISelect(type.code)}
                className={`p-4 rounded-lg border text-left transition-all ${
                  formData.mbtiType === type.code
                    ? 'border-[var(--coral-pink)] bg-[var(--coral-pink)] text-white'
                    : 'border-[var(--soft-gray)] hover:border-[var(--coral-pink)]'
                }`}
              >
                <div className="font-bold text-lg">{type.code}</div>
                <div className="text-sm opacity-90">{type.name}</div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 显示选中的MBTI结果 */}
      {selectedMBTI && (
        <div className="bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] p-6 rounded-lg text-white">
          <div className="flex items-center mb-4">
            <div className="text-3xl font-bold mr-4">{selectedMBTI.code}</div>
            <div>
              <h3 className="text-xl font-semibold">{selectedMBTI.name}</h3>
              <p className="opacity-90">{selectedMBTI.description}</p>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end mt-8">
        <Button 
          onClick={onNext} 
          size="lg"
          disabled={!formData.mbtiType}
        >
          下一步：合作偏好
        </Button>
      </div>
    </div>
  )
}
