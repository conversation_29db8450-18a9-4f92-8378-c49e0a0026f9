'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { PRESET_TAGS } from '@/data/mock-data'
import { Plus, X } from 'lucide-react'

interface TagsStepProps {
  data: any
  onDataChange: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

export default function TagsStep({ data, onDataChange, onNext }: TagsStepProps) {
  const [formData, setFormData] = useState({
    personalityTags: data.personalityTags || [],
    customTags: data.customTags || [],
    newTag: ''
  })

  useEffect(() => {
    onDataChange(formData)
  }, [formData, onDataChange])

  const handleTagToggle = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      personalityTags: prev.personalityTags.includes(tag)
        ? prev.personalityTags.filter((t: string) => t !== tag)
        : [...prev.personalityTags, tag]
    }))
  }

  const handleAddCustomTag = () => {
    if (formData.newTag.trim() && !formData.customTags.includes(formData.newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        customTags: [...prev.customTags, prev.newTag.trim()],
        newTag: ''
      }))
    }
  }

  const removeCustomTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      customTags: prev.customTags.filter((t: string) => t !== tag)
    }))
  }

  const allSelectedTags = [...formData.personalityTags, ...formData.customTags]

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">
          个性标签画像
        </h2>
        <p className="text-[var(--soft-gray)]">
          选择最能代表你的标签，构建你的个性画像
        </p>
      </div>

      {/* 性格特质标签 */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          性格特质
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {PRESET_TAGS.personality.map((tag) => (
            <button
              key={tag}
              onClick={() => handleTagToggle(tag)}
              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all ${
                formData.personalityTags.includes(tag)
                  ? 'bg-[var(--coral-pink)] text-white'
                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white'
              }`}
            >
              {tag}
            </button>
          ))}
        </div>
      </div>

      {/* 自定义标签 */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          自定义标签
        </h3>
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={formData.newTag}
            onChange={(e) => setFormData(prev => ({ ...prev, newTag: e.target.value }))}
            placeholder="添加个性化标签..."
            className="flex-1 px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent"
            onKeyPress={(e) => e.key === 'Enter' && handleAddCustomTag()}
          />
          <Button onClick={handleAddCustomTag} variant="outline">
            <Plus className="w-4 h-4" />
          </Button>
        </div>

        {formData.customTags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {formData.customTags.map((tag: string) => (
              <span
                key={tag}
                className="inline-flex items-center px-3 py-1 bg-[var(--mint-green)] text-white text-sm rounded-full"
              >
                {tag}
                <button
                  onClick={() => removeCustomTag(tag)}
                  className="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            ))}
          </div>
        )}
      </div>

      {/* 标签预览 */}
      {allSelectedTags.length > 0 && (
        <div className="bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">
            你的个性画像预览
          </h3>
          <div className="flex flex-wrap gap-2">
            {allSelectedTags.map((tag: string, index: number) => (
              <span
                key={index}
                className="px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
          <p className="text-white text-sm mt-4 opacity-90">
            已选择 {allSelectedTags.length} 个标签
          </p>
        </div>
      )}

      {/* 标签建议 */}
      <div className="bg-[var(--mist-white)] p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-3">
          💡 标签选择建议
        </h3>
        <div className="grid md:grid-cols-2 gap-4 text-sm text-[var(--soft-gray)]">
          <div>
            <strong className="text-[var(--dark-blue-gray)]">选择原则：</strong>
            <ul className="mt-2 space-y-1">
              <li>• 选择5-8个最能代表你的标签</li>
              <li>• 包含工作风格和性格特质</li>
              <li>• 避免选择过多相似标签</li>
            </ul>
          </div>
          <div>
            <strong className="text-[var(--dark-blue-gray)]">标签作用：</strong>
            <ul className="mt-2 space-y-1">
              <li>• 帮助他人快速了解你</li>
              <li>• 提高匹配准确度</li>
              <li>• 展示你的独特性</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-end mt-8">
        <Button 
          onClick={onNext} 
          size="lg"
          disabled={allSelectedTags.length === 0}
        >
          下一步：能力评估
        </Button>
      </div>
    </div>
  )
}
