'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { MBTI_TYPES, RADAR_DIMENSIONS } from '@/data/mock-data'
import { User, Briefcase, Brain, Heart, Clock, Tags, BarChart3, CheckCircle } from 'lucide-react'
import Link from 'next/link'

interface SummaryStepProps {
  data: any
  onDataChange: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

export default function SummaryStep({ data }: SummaryStepProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const selectedMBTI = MBTI_TYPES.find(type => type.code === data.mbtiType)
  const allTags = [...(data.personalityTags || []), ...(data.customTags || [])]
  const averageScore = data.radarData 
    ? (Object.values(data.radarData).reduce((sum: number, score: any) => sum + score, 0) / 6).toFixed(1)
    : '0'

  const handleSubmit = async () => {
    setIsSubmitting(true)
    // 模拟提交过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsSubmitting(false)
    setIsSubmitted(true)
  }

  if (isSubmitted) {
    return (
      <div className="text-center space-y-6">
        <div className="w-20 h-20 bg-[var(--mint-green)] rounded-full flex items-center justify-center mx-auto">
          <CheckCircle className="w-10 h-10 text-white" />
        </div>
        <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)]">
          🎉 合作画像创建成功！
        </h2>
        <p className="text-[var(--soft-gray)] max-w-md mx-auto">
          你的个人合作画像已经创建完成。现在可以查看为你推荐的合作伙伴和项目了！
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/profile">
            <Button size="lg">
              查看我的画像
            </Button>
          </Link>
          <Link href="/match">
            <Button variant="outline" size="lg">
              查看匹配结果
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">
          信息确认
        </h2>
        <p className="text-[var(--soft-gray)]">
          请确认你的合作画像信息，确认无误后即可完成创建
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* 基本信息 */}
        <div className="bg-[var(--mist-white)] p-6 rounded-lg">
          <div className="flex items-center mb-4">
            <User className="w-5 h-5 text-[var(--coral-pink)] mr-2" />
            <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)]">基本信息</h3>
          </div>
          <div className="space-y-2 text-sm">
            <div><strong>姓名：</strong>{data.name}</div>
            <div><strong>年龄：</strong>{data.age}</div>
            <div><strong>性别：</strong>{data.gender}</div>
            <div><strong>地区：</strong>{data.location}</div>
            {data.email && <div><strong>邮箱：</strong>{data.email}</div>}
          </div>
        </div>

        {/* 技能资源 */}
        <div className="bg-[var(--mist-white)] p-6 rounded-lg">
          <div className="flex items-center mb-4">
            <Briefcase className="w-5 h-5 text-[var(--mint-green)] mr-2" />
            <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)]">技能资源</h3>
          </div>
          <div className="space-y-3">
            {data.skills && data.skills.length > 0 && (
              <div>
                <strong className="text-sm">技能：</strong>
                <div className="flex flex-wrap gap-1 mt-1">
                  {data.skills.map((skill: string, index: number) => (
                    <span key={index} className="px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            )}
            {data.funding && (
              <div className="text-sm">
                <strong>资金情况：</strong>{data.funding}
              </div>
            )}
          </div>
        </div>

        {/* MBTI */}
        {selectedMBTI && (
          <div className="bg-[var(--mist-white)] p-6 rounded-lg">
            <div className="flex items-center mb-4">
              <Brain className="w-5 h-5 text-[var(--brand-purple)] mr-2" />
              <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)]">MBTI类型</h3>
            </div>
            <div className="flex items-center">
              <div className="text-2xl font-bold text-[var(--brand-purple)] mr-3">
                {selectedMBTI.code}
              </div>
              <div>
                <div className="font-semibold">{selectedMBTI.name}</div>
                <div className="text-sm text-[var(--soft-gray)]">{selectedMBTI.description}</div>
              </div>
            </div>
          </div>
        )}

        {/* 合作偏好 */}
        <div className="bg-[var(--mist-white)] p-6 rounded-lg">
          <div className="flex items-center mb-4">
            <Heart className="w-5 h-5 text-[var(--coral-pink)] mr-2" />
            <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)]">合作偏好</h3>
          </div>
          <div className="space-y-2 text-sm">
            {data.teamSize && <div><strong>团队规模：</strong>{data.teamSize}</div>}
            {data.communicationStyle && <div><strong>沟通方式：</strong>{data.communicationStyle}</div>}
            {data.workStyle && data.workStyle.length > 0 && (
              <div>
                <strong>工作方式：</strong>
                <span className="ml-1">{data.workStyle.join(', ')}</span>
              </div>
            )}
          </div>
        </div>

        {/* 时间投入 */}
        <div className="bg-[var(--mist-white)] p-6 rounded-lg">
          <div className="flex items-center mb-4">
            <Clock className="w-5 h-5 text-[var(--ice-blue)] mr-2" />
            <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)]">时间投入</h3>
          </div>
          <div className="space-y-2 text-sm">
            {data.timeAvailable && <div><strong>可投入时间：</strong>{data.timeAvailable}</div>}
            {data.commitment && <div><strong>承诺程度：</strong>{data.commitment}</div>}
            {data.timeline && <div><strong>期望时间线：</strong>{data.timeline}</div>}
          </div>
        </div>

        {/* 个性标签 */}
        {allTags.length > 0 && (
          <div className="bg-[var(--mist-white)] p-6 rounded-lg">
            <div className="flex items-center mb-4">
              <Tags className="w-5 h-5 text-[var(--mint-green)] mr-2" />
              <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)]">个性标签</h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {allTags.map((tag: string, index: number) => (
                <span key={index} className="px-3 py-1 bg-[var(--coral-pink)] text-white text-sm rounded-full">
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 能力雷达图总结 */}
      {data.radarData && (
        <div className="bg-white p-6 rounded-lg card-shadow">
          <div className="flex items-center mb-4">
            <BarChart3 className="w-5 h-5 text-[var(--brand-purple)] mr-2" />
            <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)]">能力评估</h3>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {RADAR_DIMENSIONS.map((dim) => (
              <div key={dim.key} className="text-center">
                <div className="text-2xl font-bold text-[var(--brand-purple)]">
                  {data.radarData[dim.key]}
                </div>
                <div className="text-sm text-[var(--dark-blue-gray)]">{dim.label}</div>
              </div>
            ))}
          </div>
          <div className="text-center mt-4 pt-4 border-t">
            <div className="text-sm text-[var(--soft-gray)]">综合评分</div>
            <div className="text-3xl font-bold text-[var(--coral-pink)]">{averageScore}</div>
          </div>
        </div>
      )}

      {/* 隐私说明 */}
      <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-2">🔒 隐私保护说明</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 你的个人信息将被安全保护，不会泄露给第三方</li>
          <li>• 你可以随时修改或删除你的合作画像</li>
          <li>• 匹配过程中只会显示必要的合作相关信息</li>
          <li>• 联系方式只有在双方同意后才会共享</li>
        </ul>
      </div>

      <div className="flex justify-center mt-8">
        <Button 
          onClick={handleSubmit}
          size="lg"
          disabled={isSubmitting}
          className="px-8"
        >
          {isSubmitting ? '创建中...' : '确认创建合作画像'}
        </Button>
      </div>
    </div>
  )
}
