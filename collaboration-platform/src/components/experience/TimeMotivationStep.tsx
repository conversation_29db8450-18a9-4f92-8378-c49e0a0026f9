'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Clock, Target, Heart } from 'lucide-react'

interface TimeMotivationStepProps {
  data: any
  onDataChange: (data: any) => void
  onNext: () => void
  onPrev: () => void
}

export default function TimeMotivationStep({ data, onDataChange, onNext }: TimeMotivationStepProps) {
  const [formData, setFormData] = useState({
    timeAvailable: data.timeAvailable || '',
    timePreference: data.timePreference || '',
    commitment: data.commitment || '',
    motivation: data.motivation || [],
    goals: data.goals || '',
    timeline: data.timeline || ''
  })

  useEffect(() => {
    onDataChange(formData)
  }, [formData, onDataChange])

  const handleMotivationToggle = (motivation: string) => {
    setFormData(prev => ({
      ...prev,
      motivation: prev.motivation.includes(motivation)
        ? prev.motivation.filter((m: string) => m !== motivation)
        : [...prev.motivation, motivation]
    }))
  }

  const motivations = [
    '学习新技能',
    '获得收入',
    '积累经验',
    '扩展人脉',
    '实现创意',
    '解决问题',
    '帮助他人',
    '个人成长',
    '行业影响力',
    '创业梦想'
  ]

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">
          时间投入与动机
        </h2>
        <p className="text-[var(--soft-gray)]">
          了解你的时间安排和参与动机
        </p>
      </div>

      {/* 可投入时间 */}
      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <div className="flex items-center mb-4">
            <Clock className="w-5 h-5 text-[var(--coral-pink)] mr-2" />
            <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)]">
              每周可投入时间
            </h3>
          </div>
          <div className="space-y-3">
            {[
              '5小时以下（业余时间）',
              '5-15小时（兼职投入）',
              '15-30小时（半职投入）',
              '30小时以上（全职投入）'
            ].map((time) => (
              <button
                key={time}
                onClick={() => setFormData(prev => ({ ...prev, timeAvailable: time }))}
                className={`w-full px-4 py-3 rounded-lg text-left transition-all ${
                  formData.timeAvailable === time
                    ? 'bg-[var(--coral-pink)] text-white'
                    : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white'
                }`}
              >
                {time}
              </button>
            ))}
          </div>
        </div>

        <div>
          <div className="flex items-center mb-4">
            <Target className="w-5 h-5 text-[var(--mint-green)] mr-2" />
            <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)]">
              时间偏好
            </h3>
          </div>
          <div className="space-y-3">
            {[
              '工作日晚上',
              '周末集中时间',
              '灵活安排',
              '固定时间段'
            ].map((pref) => (
              <button
                key={pref}
                onClick={() => setFormData(prev => ({ ...prev, timePreference: pref }))}
                className={`w-full px-4 py-3 rounded-lg text-left transition-all ${
                  formData.timePreference === pref
                    ? 'bg-[var(--mint-green)] text-white'
                    : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--mint-green)] hover:text-white'
                }`}
              >
                {pref}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 承诺程度 */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">
          项目承诺程度
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[
            { 
              level: '轻度参与', 
              desc: '尝试性参与，可随时退出',
              color: 'var(--ice-blue)'
            },
            { 
              level: '中度承诺', 
              desc: '认真参与，但保持灵活性',
              color: 'var(--mint-green)'
            },
            { 
              level: '高度承诺', 
              desc: '全力投入，长期合作',
              color: 'var(--coral-pink)'
            }
          ].map((item) => (
            <button
              key={item.level}
              onClick={() => setFormData(prev => ({ ...prev, commitment: item.level }))}
              className={`p-4 rounded-lg border-2 text-left transition-all ${
                formData.commitment === item.level
                  ? `border-[${item.color}] bg-[${item.color}] text-white`
                  : 'border-[var(--soft-gray)] hover:border-[var(--coral-pink)]'
              }`}
            >
              <div className="font-semibold mb-2">{item.level}</div>
              <div className="text-sm opacity-90">{item.desc}</div>
            </button>
          ))}
        </div>
      </div>

      {/* 参与动机 */}
      <div>
        <div className="flex items-center mb-4">
          <Heart className="w-5 h-5 text-[var(--brand-purple)] mr-2" />
          <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)]">
            参与动机（可多选）
          </h3>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
          {motivations.map((motivation) => (
            <button
              key={motivation}
              onClick={() => handleMotivationToggle(motivation)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                formData.motivation.includes(motivation)
                  ? 'bg-[var(--brand-purple)] text-white'
                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--brand-purple)] hover:text-white'
              }`}
            >
              {motivation}
            </button>
          ))}
        </div>
      </div>

      {/* 目标描述 */}
      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
            个人目标
          </label>
          <textarea
            value={formData.goals}
            onChange={(e) => setFormData(prev => ({ ...prev, goals: e.target.value }))}
            rows={4}
            className="w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent"
            placeholder="描述你希望通过合作实现的个人目标..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">
            期望时间线
          </label>
          <select
            value={formData.timeline}
            onChange={(e) => setFormData(prev => ({ ...prev, timeline: e.target.value }))}
            className="w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent mb-4"
          >
            <option value="">选择项目时间线</option>
            <option value="1-3months">1-3个月短期项目</option>
            <option value="3-6months">3-6个月中期项目</option>
            <option value="6-12months">6-12个月长期项目</option>
            <option value="1year+">1年以上持续项目</option>
          </select>
          
          <div className="text-sm text-[var(--soft-gray)] bg-[var(--mist-white)] p-3 rounded-lg">
            💡 <strong>提示：</strong>明确的时间预期有助于找到合适的合作伙伴
          </div>
        </div>
      </div>

      <div className="flex justify-end mt-8">
        <Button onClick={onNext} size="lg">
          下一步：标签画像
        </Button>
      </div>
    </div>
  )
}
