'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { MOCK_USERS, MOCK_PROJECTS, MBTI_TYPES } from '@/data/mock-data'
import { Heart, MessageCircle, User, Users, Target, Clock, Star, Filter } from 'lucide-react'
import Link from 'next/link'

export default function MatchPage() {
  const [activeTab, setActiveTab] = useState<'people' | 'projects'>('people')
  const [filterOpen, setFilterOpen] = useState(false)

  const currentUser = MOCK_USERS[0]
  const matchedUsers = MOCK_USERS.slice(1)
  const matchedProjects = MOCK_PROJECTS

  return (
    <div className="min-h-screen gradient-bg">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]">
              ← 返回首页
            </Link>
            <h1 className="text-xl font-semibold text-[var(--dark-blue-gray)]">
              智能匹配推荐
            </h1>
            <Button variant="outline" size="sm" onClick={() => setFilterOpen(!filterOpen)}>
              <Filter className="w-4 h-4 mr-2" />
              筛选
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 标签页切换 */}
        <div className="flex items-center justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-sm">
            <button
              onClick={() => setActiveTab('people')}
              className={`px-6 py-2 rounded-md font-medium transition-all ${
                activeTab === 'people'
                  ? 'bg-[var(--coral-pink)] text-white'
                  : 'text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)]'
              }`}
            >
              <Users className="w-4 h-4 inline mr-2" />
              合作伙伴
            </button>
            <button
              onClick={() => setActiveTab('projects')}
              className={`px-6 py-2 rounded-md font-medium transition-all ${
                activeTab === 'projects'
                  ? 'bg-[var(--coral-pink)] text-white'
                  : 'text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)]'
              }`}
            >
              <Target className="w-4 h-4 inline mr-2" />
              推荐项目
            </button>
          </div>
        </div>

        {/* 合作伙伴匹配 */}
        {activeTab === 'people' && (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">
                为你推荐的合作伙伴
              </h2>
              <p className="text-[var(--soft-gray)]">
                基于你的技能、性格和偏好，我们为你找到了这些潜在的合作伙伴
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              {matchedUsers.map((user) => {
                const mbtiType = MBTI_TYPES.find(type => type.code === user.mbti)
                return (
                  <div key={user.id} className="bg-white rounded-xl card-shadow card-hover p-6">
                    {/* 匹配度指示器 */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] rounded-full flex items-center justify-center mr-3">
                          <User className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-[var(--dark-blue-gray)]">{user.name}</h3>
                          <p className="text-sm text-[var(--soft-gray)]">{user.age}岁 · {user.location}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-[var(--mint-green)]">{user.compatibility}%</div>
                        <div className="text-xs text-[var(--soft-gray)]">匹配度</div>
                      </div>
                    </div>

                    {/* MBTI */}
                    {mbtiType && (
                      <div className="bg-[var(--mist-white)] p-3 rounded-lg mb-4">
                        <div className="flex items-center">
                          <span className="font-bold text-[var(--brand-purple)] mr-2">{mbtiType.code}</span>
                          <span className="text-sm text-[var(--dark-blue-gray)]">{mbtiType.name}</span>
                        </div>
                      </div>
                    )}

                    {/* 技能标签 */}
                    <div className="mb-4">
                      <div className="text-sm font-medium text-[var(--dark-blue-gray)] mb-2">核心技能</div>
                      <div className="flex flex-wrap gap-1">
                        {user.skills.map((skill, index) => (
                          <span 
                            key={index}
                            className="px-2 py-1 bg-[var(--coral-pink)] text-white text-xs rounded"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* 个性标签 */}
                    <div className="mb-4">
                      <div className="text-sm font-medium text-[var(--dark-blue-gray)] mb-2">个性特质</div>
                      <div className="flex flex-wrap gap-1">
                        {user.tags.map((tag, index) => (
                          <span 
                            key={index}
                            className="px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* 匹配原因 */}
                    <div className="bg-blue-50 p-3 rounded-lg mb-4">
                      <div className="text-sm font-medium text-blue-800 mb-1">匹配原因</div>
                      <div className="text-xs text-blue-600">
                        技能互补度高 · 性格类型匹配 · 工作方式相似
                      </div>
                    </div>

                    {/* 行动按钮 */}
                    <div className="flex gap-2">
                      <Button size="sm" className="flex-1">
                        <MessageCircle className="w-4 h-4 mr-1" />
                        发起聊天
                      </Button>
                      <Button variant="outline" size="sm">
                        <Heart className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* 项目匹配 */}
        {activeTab === 'projects' && (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">
                推荐项目
              </h2>
              <p className="text-[var(--soft-gray)]">
                根据你的技能和兴趣，这些项目可能很适合你参与
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              {matchedProjects.map((project) => (
                <div key={project.id} className="bg-white rounded-xl card-shadow card-hover p-6">
                  {/* 项目头部 */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-2">
                        {project.title}
                      </h3>
                      <p className="text-[var(--soft-gray)] text-sm mb-3">
                        {project.description}
                      </p>
                    </div>
                    <div className="text-right ml-4">
                      <div className="text-2xl font-bold text-[var(--mint-green)]">{project.compatibility}%</div>
                      <div className="text-xs text-[var(--soft-gray)]">匹配度</div>
                    </div>
                  </div>

                  {/* 项目标签 */}
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2">
                      {project.tags.map((tag, index) => (
                        <span 
                          key={index}
                          className="px-3 py-1 bg-[var(--ice-blue)] text-white text-sm rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* 项目信息 */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="flex items-center">
                      <Users className="w-4 h-4 text-[var(--soft-gray)] mr-2" />
                      <span className="text-sm">团队 {project.teamSize} 人</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 text-[var(--soft-gray)] mr-2" />
                      <span className="text-sm">进度 {project.progress}%</span>
                    </div>
                  </div>

                  {/* 进度条 */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-[var(--dark-blue-gray)]">项目进度</span>
                      <span className="text-[var(--soft-gray)]">{project.progress}%</span>
                    </div>
                    <div className="w-full bg-[var(--mist-white)] rounded-full h-2">
                      <div 
                        className="bg-[var(--mint-green)] h-2 rounded-full transition-all"
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* 匹配原因 */}
                  <div className="bg-green-50 p-3 rounded-lg mb-4">
                    <div className="text-sm font-medium text-green-800 mb-1">推荐原因</div>
                    <div className="text-xs text-green-600">
                      技能需求匹配 · 项目类型符合兴趣 · 团队规模合适
                    </div>
                  </div>

                  {/* 行动按钮 */}
                  <div className="flex gap-2">
                    <Button size="sm" className="flex-1">
                      申请加入
                    </Button>
                    <Button variant="outline" size="sm">
                      <Star className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 底部提示 */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-lg p-6 card-shadow">
            <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-2">
              没有找到合适的匹配？
            </h3>
            <p className="text-[var(--soft-gray)] mb-4">
              我们会持续为你寻找新的合作机会，也可以完善你的画像信息来获得更精准的推荐
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link href="/profile">
                <Button variant="outline">
                  完善画像信息
                </Button>
              </Link>
              <Button>
                设置推荐提醒
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
