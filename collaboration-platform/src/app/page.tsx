'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Users, Target, Heart, Zap } from 'lucide-react'
import Link from 'next/link'

export default function Home() {
  return (
    <div className="min-h-screen gradient-bg">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-[var(--dark-blue-gray)] mb-6">
              找到你的
              <span className="text-[var(--coral-pink)]"> 完美合作伙伴</span>
            </h1>
            <p className="text-xl md:text-2xl text-[var(--soft-gray)] mb-8 max-w-3xl mx-auto">
              通过科学的性格分析和智能匹配算法，为你找到最适合的项目团队和合作伙伴
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/experience">
                <Button size="lg" className="w-full sm:w-auto">
                  开始体验
                </Button>
              </Link>
              <Link href="/about">
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  了解更多
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--dark-blue-gray)] mb-4">
              为什么选择我们？
            </h2>
            <p className="text-lg text-[var(--soft-gray)] max-w-2xl mx-auto">
              基于科学的匹配算法，让合作更高效、更愉悦、更可信赖
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FeatureCard
              icon={<Users className="w-8 h-8" />}
              title="精准匹配"
              description="基于MBTI性格测试和能力评估，为你匹配最合适的合作伙伴"
              color="var(--coral-pink)"
            />
            <FeatureCard
              icon={<Target className="w-8 h-8" />}
              title="科学评估"
              description="六维能力雷达图，全面展示个人协作画像"
              color="var(--mint-green)"
            />
            <FeatureCard
              icon={<Heart className="w-8 h-8" />}
              title="信任机制"
              description="透明的评价体系，建立可信赖的合作环境"
              color="var(--brand-purple)"
            />
            <FeatureCard
              icon={<Zap className="w-8 h-8" />}
              title="高效协作"
              description="智能推荐项目和团队，提升合作成功率"
              color="var(--ice-blue)"
            />
          </div>
        </div>
      </section>

      {/* Pending Projects */}
      <section className="py-20 gradient-bg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--dark-blue-gray)] mb-4">
              待启动项目
            </h2>
            <p className="text-lg text-[var(--soft-gray)]">
              社区已经立项或发起的召集项目，等待合适的合作伙伴加入
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <ProjectCard
              title="AI驱动的学习平台"
              description="基于机器学习的个性化学习推荐系统，需要前端开发、AI算法和产品设计人才"
              tags={['人工智能', '教育科技', '产品设计']}
              status="寻找合作伙伴"
              needed={['前端开发工程师', 'AI算法工程师']}
            />
            <ProjectCard
              title="可持续生活社区"
              description="连接环保爱好者的社交平台，推广可持续生活方式，需要全栈开发和运营人才"
              tags={['社交网络', '环保', '移动应用']}
              status="项目启动中"
              needed={['全栈开发工程师', '社区运营']}
            />
            <ProjectCard
              title="Web3创作者平台"
              description="基于区块链的创作者经济平台，需要区块链开发、前端和商务拓展人才"
              tags={['区块链', 'Web3', '创作者经济']}
              status="概念验证阶段"
              needed={['区块链开发', '商务拓展']}
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-[var(--coral-pink)]">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            准备好开始你的合作之旅了吗？
          </h2>
          <p className="text-xl text-white/90 mb-8">
            只需几分钟，就能创建你的专属合作画像
          </p>
          <Link href="/experience">
            <Button variant="secondary" size="lg">
              立即开始体验
            </Button>
          </Link>
        </div>
      </section>
    </div>
  )
}

interface FeatureCardProps {
  icon: React.ReactNode
  title: string
  description: string
  color: string
}

function FeatureCard({ icon, title, description, color }: FeatureCardProps) {
  return (
    <div className="text-center p-6 rounded-xl card-shadow card-hover bg-white">
      <div
        className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
        style={{ backgroundColor: `${color}20`, color }}
      >
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-[var(--dark-blue-gray)] mb-2">
        {title}
      </h3>
      <p className="text-[var(--soft-gray)]">
        {description}
      </p>
    </div>
  )
}

interface ProjectCardProps {
  title: string
  description: string
  tags: string[]
  status: string
  needed: string[]
}

function ProjectCard({ title, description, tags, status, needed }: ProjectCardProps) {
  return (
    <div className="bg-white p-6 rounded-xl card-shadow card-hover">
      <h3 className="text-xl font-semibold text-[var(--dark-blue-gray)] mb-3">
        {title}
      </h3>
      <p className="text-[var(--soft-gray)] mb-4">
        {description}
      </p>
      <div className="flex flex-wrap gap-2 mb-4">
        {tags.map((tag, index) => (
          <span
            key={index}
            className="px-3 py-1 bg-[var(--mist-white)] text-[var(--dark-blue-gray)] text-sm rounded-full"
          >
            {tag}
          </span>
        ))}
      </div>
      <div className="mb-3">
        <div className="text-sm font-medium text-[var(--dark-blue-gray)] mb-2">急需人才：</div>
        <div className="flex flex-wrap gap-1">
          {needed.map((role, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-[var(--coral-pink)] text-white text-xs rounded"
            >
              {role}
            </span>
          ))}
        </div>
      </div>
      <div className="text-[var(--mint-green)] font-semibold">
        📋 {status}
      </div>
    </div>
  )
}
