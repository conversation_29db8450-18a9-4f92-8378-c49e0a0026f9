'use client'

import { But<PERSON> } from '@/components/ui/button'
import { MBTI_TYPES, RADAR_DIMENSIONS, MOCK_USERS } from '@/data/mock-data'
import { User, Mail, MapPin, Edit, Share2, Settings } from 'lucide-react'
import { Radar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, ResponsiveContainer } from 'recharts'
import Link from 'next/link'

export default function ProfilePage() {
  // 使用模拟数据
  const user = MOCK_USERS[0]
  const mbtiType = MBTI_TYPES.find(type => type.code === user.mbti)
  
  const radarChartData = RADAR_DIMENSIONS.map(dim => ({
    dimension: dim.label,
    value: user.radarData[dim.key],
    fullMark: 10
  }))

  return (
    <div className="min-h-screen gradient-bg">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]">
              ← 返回首页
            </Link>
            <h1 className="text-xl font-semibold text-[var(--dark-blue-gray)]">
              我的合作画像
            </h1>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Edit className="w-4 h-4 mr-2" />
                编辑
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                分享
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* 左侧：基本信息 */}
          <div className="lg:col-span-1 space-y-6">
            {/* 个人信息卡片 */}
            <div className="bg-white rounded-xl card-shadow p-6">
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] rounded-full flex items-center justify-center mx-auto mb-4">
                  <User className="w-10 h-10 text-white" />
                </div>
                <h2 className="text-xl font-bold text-[var(--dark-blue-gray)]">{user.name}</h2>
                <p className="text-[var(--soft-gray)]">{user.age}岁</p>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <MapPin className="w-4 h-4 text-[var(--soft-gray)] mr-2" />
                  <span>{user.location}</span>
                </div>
                <div className="flex items-center text-sm">
                  <Mail className="w-4 h-4 text-[var(--soft-gray)] mr-2" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>

            {/* MBTI卡片 */}
            {mbtiType && (
              <div className="bg-gradient-to-r from-[var(--brand-purple)] to-[var(--ice-blue)] rounded-xl p-6 text-white">
                <h3 className="text-lg font-semibold mb-4">MBTI性格类型</h3>
                <div className="flex items-center mb-3">
                  <div className="text-3xl font-bold mr-4">{mbtiType.code}</div>
                  <div>
                    <div className="font-semibold">{mbtiType.name}</div>
                    <div className="text-sm opacity-90">{mbtiType.description}</div>
                  </div>
                </div>
              </div>
            )}

            {/* 技能标签 */}
            <div className="bg-white rounded-xl card-shadow p-6">
              <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">核心技能</h3>
              <div className="flex flex-wrap gap-2">
                {user.skills.map((skill, index) => (
                  <span 
                    key={index}
                    className="px-3 py-1 bg-[var(--coral-pink)] text-white text-sm rounded-full"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>

            {/* 个性标签 */}
            <div className="bg-white rounded-xl card-shadow p-6">
              <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-4">个性标签</h3>
              <div className="flex flex-wrap gap-2">
                {user.tags.map((tag, index) => (
                  <span 
                    key={index}
                    className="px-3 py-1 bg-[var(--mint-green)] text-white text-sm rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* 右侧：能力雷达图和详细信息 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 能力雷达图 */}
            <div className="bg-white rounded-xl card-shadow p-6">
              <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-6">六维能力评估</h3>
              <div className="grid lg:grid-cols-2 gap-8">
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart data={radarChartData}>
                      <PolarGrid />
                      <PolarAngleAxis 
                        dataKey="dimension" 
                        tick={{ fontSize: 12, fill: '#7F8C8D' }}
                      />
                      <PolarRadiusAxis 
                        angle={90} 
                        domain={[0, 10]} 
                        tick={{ fontSize: 10, fill: '#BDC3C7' }}
                      />
                      <Radar
                        name="能力值"
                        dataKey="value"
                        stroke="#FF6B6B"
                        fill="#FF6B6B"
                        fillOpacity={0.3}
                        strokeWidth={2}
                      />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
                
                <div className="space-y-4">
                  {RADAR_DIMENSIONS.map((dim) => (
                    <div key={dim.key} className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-[var(--dark-blue-gray)]">{dim.label}</div>
                        <div className="text-sm text-[var(--soft-gray)]">{dim.description}</div>
                      </div>
                      <div className="text-2xl font-bold text-[var(--coral-pink)]">
                        {user.radarData[dim.key]}
                      </div>
                    </div>
                  ))}
                  
                  <div className="pt-4 border-t">
                    <div className="text-center">
                      <div className="text-sm text-[var(--soft-gray)]">综合评分</div>
                      <div className="text-3xl font-bold text-[var(--coral-pink)]">
                        {(Object.values(user.radarData).reduce((sum, score) => sum + score, 0) / 6).toFixed(1)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 合作偏好 */}
            <div className="bg-white rounded-xl card-shadow p-6">
              <h3 className="text-lg font-semibold text-[var(--dark-blue-gray)] mb-6">合作偏好</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-[var(--dark-blue-gray)] mb-3">工作方式</h4>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3"></div>
                      <span className="text-sm">远程工作</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3"></div>
                      <span className="text-sm">灵活时间</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3"></div>
                      <span className="text-sm">深度思考</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-[var(--dark-blue-gray)] mb-3">团队偏好</h4>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3"></div>
                      <span className="text-sm">2-3人小团队</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3"></div>
                      <span className="text-sm">技术导向</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3"></div>
                      <span className="text-sm">中度承诺</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 行动按钮 */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/match" className="flex-1">
                <Button size="lg" className="w-full">
                  查看匹配推荐
                </Button>
              </Link>
              <Link href="/experience" className="flex-1">
                <Button variant="outline" size="lg" className="w-full">
                  更新画像信息
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
