'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { ArrowLeft, Globe, Users, Target, Shield, CheckCircle, AlertTriangle, Lightbulb, Heart } from 'lucide-react'
import Link from 'next/link'

export default function AboutPage() {
  return (
    <div className="min-h-screen gradient-bg">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)] transition-colors">
              <ArrowLeft className="w-5 h-5 mr-2" />
              <span className="hidden sm:inline">返回首页</span>
              <span className="sm:hidden">返回</span>
            </Link>
            <h1 className="text-lg sm:text-xl font-semibold text-[var(--dark-blue-gray)]">
              项目说明
            </h1>
            <div className="w-16 sm:w-20"></div> {/* Spacer for centering */}
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Hero Section */}
        <section className="text-center mb-8 sm:mb-12">
          <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
            <Globe className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
          </div>
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[var(--dark-blue-gray)] mb-3 sm:mb-4">
            项目愿景与合作说明
          </h1>
          <p className="text-base sm:text-lg text-[var(--soft-gray)] max-w-2xl mx-auto leading-relaxed">
            我们正在创造一种未来的工作方式
          </p>
        </section>

        {/* Vision Section */}
        <section className="bg-white rounded-xl card-shadow p-6 sm:p-8 mb-6 sm:mb-8">
          <div className="flex items-center mb-4 sm:mb-6">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-[var(--coral-pink)] bg-opacity-20 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
              <Heart className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--coral-pink)]" />
            </div>
            <h2 className="text-xl sm:text-2xl font-bold text-[var(--dark-blue-gray)]">
              我们正在创造一种未来的工作方式
            </h2>
          </div>
          
          <div className="prose prose-sm sm:prose-base max-w-none">
            <p className="text-[var(--soft-gray)] mb-4 sm:mb-6 leading-relaxed">
              随着远程协作和数字工具的发展，<strong className="text-[var(--dark-blue-gray)]">未来的工作将更去中心化、更自由、更以个人能力为单位形成项目制组织</strong>。不再依赖传统公司、地理位置或全职关系，每个人都可以因能力、想法或资源参与价值共创。
            </p>
            
            <div className="bg-[var(--mist-white)] p-4 sm:p-6 rounded-lg">
              <h3 className="text-lg sm:text-xl font-semibold text-[var(--dark-blue-gray)] mb-3 sm:mb-4">
                我们希望搭建这样一个平台：
              </h3>
              <ul className="space-y-2 sm:space-y-3">
                <li className="flex items-start">
                  <span className="text-[var(--coral-pink)] mr-2 sm:mr-3 mt-1">👉</span>
                  <span className="text-[var(--dark-blue-gray)]">聚集有想法、有技能、有资源、有时间的你们</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--coral-pink)] mr-2 sm:mr-3 mt-1">👉</span>
                  <span className="text-[var(--dark-blue-gray)]">让每个人都能在线上找到合适的合作伙伴和项目方向</span>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--coral-pink)] mr-2 sm:mr-3 mt-1">👉</span>
                  <span className="text-[var(--dark-blue-gray)]">在真实的协作中积累经验、建立作品、获得回报</span>
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Pain Points Section */}
        <section className="bg-white rounded-xl card-shadow p-6 sm:p-8 mb-6 sm:mb-8">
          <div className="flex items-center mb-4 sm:mb-6">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-[var(--mint-green)] bg-opacity-20 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
              <Target className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--mint-green)]" />
            </div>
            <h2 className="text-xl sm:text-2xl font-bold text-[var(--dark-blue-gray)]">
              为什么我们需要这个平台？
            </h2>
          </div>

          <div className="mb-4 sm:mb-6">
            <h3 className="text-lg sm:text-xl font-semibold text-[var(--dark-blue-gray)] mb-3 sm:mb-4 flex items-center">
              <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
              线上协作的现实痛点
            </h3>
            <div className="grid sm:grid-cols-2 gap-3 sm:gap-4">
              <div className="bg-red-50 border border-red-200 p-3 sm:p-4 rounded-lg">
                <p className="text-sm sm:text-base text-red-800">很难找到<strong>靠谱、能力互补</strong>的合作者</p>
              </div>
              <div className="bg-red-50 border border-red-200 p-3 sm:p-4 rounded-lg">
                <p className="text-sm sm:text-base text-red-800">信息不对称，匹配靠运气</p>
              </div>
              <div className="bg-red-50 border border-red-200 p-3 sm:p-4 rounded-lg">
                <p className="text-sm sm:text-base text-red-800">协作缺少结构，信任成本高</p>
              </div>
              <div className="bg-red-50 border border-red-200 p-3 sm:p-4 rounded-lg">
                <p className="text-sm sm:text-base text-red-800">没有明确机制来保障<strong>投入和收益</strong></p>
              </div>
            </div>
          </div>
        </section>

        {/* Solutions Section */}
        <section className="bg-white rounded-xl card-shadow p-6 sm:p-8 mb-6 sm:mb-8">
          <div className="flex items-center mb-4 sm:mb-6">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-[var(--brand-purple)] bg-opacity-20 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
              <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-[var(--brand-purple)]" />
            </div>
            <h2 className="text-xl sm:text-2xl font-bold text-[var(--dark-blue-gray)]">
              我们如何提升线上协作的质量？
            </h2>
          </div>

          <div className="space-y-6 sm:space-y-8">
            {/* Solution 1 */}
            <div className="border-l-4 border-[var(--coral-pink)] pl-4 sm:pl-6">
              <h3 className="text-lg sm:text-xl font-semibold text-[var(--dark-blue-gray)] mb-2 sm:mb-3">
                ✅ 多维用户画像匹配系统
              </h3>
              <ul className="space-y-1 sm:space-y-2 text-sm sm:text-base text-[var(--soft-gray)]">
                <li>• 结合技能、资源、时间、合作偏好、MBTI 性格、标签画像进行智能撮合</li>
                <li>• 每个人都有自己的"数字合作画像"，找到互补的团队成员</li>
              </ul>
            </div>

            {/* Solution 2 */}
            <div className="border-l-4 border-[var(--mint-green)] pl-4 sm:pl-6">
              <h3 className="text-lg sm:text-xl font-semibold text-[var(--dark-blue-gray)] mb-2 sm:mb-3">
                ✅ 分角色的项目机制
              </h3>
              <ul className="space-y-1 sm:space-y-2 text-sm sm:text-base text-[var(--soft-gray)]">
                <li>• 每个项目可设置：发起人、执行人、技术支持、沟通协调等不同职责</li>
                <li>• 明确分工，让每种能力都能被看见、被利用、被尊重</li>
              </ul>
            </div>

            {/* Solution 3 */}
            <div className="border-l-4 border-[var(--brand-purple)] pl-4 sm:pl-6">
              <h3 className="text-lg sm:text-xl font-semibold text-[var(--dark-blue-gray)] mb-2 sm:mb-3">
                ✅ 透明的合作意向与收益机制
                <span className="text-sm text-[var(--soft-gray)] font-normal ml-2">(阶段设计中)</span>
              </h3>
              <ul className="space-y-1 sm:space-y-2 text-sm sm:text-base text-[var(--soft-gray)]">
                <li>• 平台记录项目贡献轨迹</li>
                <li>• 可探索代币激励、项目评分、DAO式治理等方式保障收益分配公平</li>
                <li>• 合作后可以进行互评和信用积累，帮助建立长效信任网络</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Prevention Table Section */}
        <section className="bg-white rounded-xl card-shadow p-6 sm:p-8 mb-6 sm:mb-8">
          <div className="flex items-center mb-4 sm:mb-6">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
              <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-red-600" />
            </div>
            <h2 className="text-xl sm:text-2xl font-bold text-[var(--dark-blue-gray)]">
              线上合作失败的常见原因我们如何预防？
            </h2>
          </div>

          {/* Mobile-friendly table */}
          <div className="space-y-4 sm:hidden">
            <div className="bg-[var(--mist-white)] p-4 rounded-lg">
              <h4 className="font-semibold text-[var(--dark-blue-gray)] mb-2">缺乏信任</h4>
              <p className="text-sm text-[var(--soft-gray)]">个性测试 + 价值观偏好匹配 + 合作评价体系</p>
            </div>
            <div className="bg-[var(--mist-white)] p-4 rounded-lg">
              <h4 className="font-semibold text-[var(--dark-blue-gray)] mb-2">沟通风格差异</h4>
              <p className="text-sm text-[var(--soft-gray)]">MBTI人格维度 + 合作偏好标签提前暴露</p>
            </div>
            <div className="bg-[var(--mist-white)] p-4 rounded-lg">
              <h4 className="font-semibold text-[var(--dark-blue-gray)] mb-2">不清楚自己能做什么</h4>
              <p className="text-sm text-[var(--soft-gray)]">标签选择 + 六边型能力自评 + 系统引导自我表达</p>
            </div>
            <div className="bg-[var(--mist-white)] p-4 rounded-lg">
              <h4 className="font-semibold text-[var(--dark-blue-gray)] mb-2">没人主导项目</h4>
              <p className="text-sm text-[var(--soft-gray)]">分配发起人角色 + 系统跟踪任务进展</p>
            </div>
            <div className="bg-[var(--mist-white)] p-4 rounded-lg">
              <h4 className="font-semibold text-[var(--dark-blue-gray)] mb-2">收益难分配</h4>
              <p className="text-sm text-[var(--soft-gray)]">探索平台智能贡献记录 + 多方可见的收益协商流程</p>
            </div>
          </div>

          {/* Desktop table */}
          <div className="hidden sm:block overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b-2 border-[var(--soft-gray)]">
                  <th className="text-left py-3 px-4 font-semibold text-[var(--dark-blue-gray)]">问题</th>
                  <th className="text-left py-3 px-4 font-semibold text-[var(--dark-blue-gray)]">解决方式</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-4 text-[var(--dark-blue-gray)]">缺乏信任</td>
                  <td className="py-3 px-4 text-[var(--soft-gray)]">个性测试 + 价值观偏好匹配 + 合作评价体系</td>
                </tr>
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-4 text-[var(--dark-blue-gray)]">沟通风格差异</td>
                  <td className="py-3 px-4 text-[var(--soft-gray)]">MBTI人格维度 + 合作偏好标签提前暴露</td>
                </tr>
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-4 text-[var(--dark-blue-gray)]">不清楚自己能做什么</td>
                  <td className="py-3 px-4 text-[var(--soft-gray)]">标签选择 + 六边型能力自评 + 系统引导自我表达</td>
                </tr>
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-4 text-[var(--dark-blue-gray)]">没人主导项目</td>
                  <td className="py-3 px-4 text-[var(--soft-gray)]">分配发起人角色 + 系统跟踪任务进展</td>
                </tr>
                <tr>
                  <td className="py-3 px-4 text-[var(--dark-blue-gray)]">收益难分配</td>
                  <td className="py-3 px-4 text-[var(--soft-gray)]">探索平台智能贡献记录 + 多方可见的收益协商流程</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        {/* Value Section */}
        <section className="bg-white rounded-xl card-shadow p-6 sm:p-8 mb-6 sm:mb-8">
          <div className="flex items-center mb-4 sm:mb-6">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
              <Lightbulb className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-600" />
            </div>
            <h2 className="text-xl sm:text-2xl font-bold text-[var(--dark-blue-gray)]">
              每个人都有可被看见的价值
            </h2>
          </div>

          <div className="prose prose-sm sm:prose-base max-w-none">
            <p className="text-[var(--soft-gray)] mb-4 sm:mb-6 leading-relaxed">
              你也许没有程序能力，但你有创意、有理解人性的能力、有商业眼光、有推广资源。这个平台会帮助你<strong className="text-[var(--dark-blue-gray)]">表达、展示、连接、协作、共同成长</strong>。
            </p>

            <div className="bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] p-4 sm:p-6 rounded-lg text-white">
              <p className="text-base sm:text-lg font-medium mb-2">
                这不仅仅是一个找队友的地方——
              </p>
              <p className="text-sm sm:text-base opacity-90">
                它是下一代数字化工作方式的孵化器，是个人价值的放大器。
              </p>
            </div>
          </div>
        </section>

        {/* Privacy Section */}
        <section className="bg-white rounded-xl card-shadow p-6 sm:p-8 mb-6 sm:mb-8">
          <div className="flex items-center mb-4 sm:mb-6">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
              <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-green-600" />
            </div>
            <h2 className="text-xl sm:text-2xl font-bold text-[var(--dark-blue-gray)]">
              数据与隐私声明
            </h2>
          </div>

          <div className="bg-green-50 border border-green-200 p-4 sm:p-6 rounded-lg">
            <p className="text-sm sm:text-base text-green-800 leading-relaxed">
              🔐 你的所有信息仅用于平台匹配与合作，不会向第三方泄露。你将拥有对所有资料的可视化编辑权和提交控制权。
            </p>
          </div>
        </section>

        {/* CTA Section */}
        <section className="text-center bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] rounded-xl p-6 sm:p-8 text-white">
          <h2 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4">
            准备开始你的数字协作之旅？
          </h2>
          <p className="text-sm sm:text-base opacity-90 mb-4 sm:mb-6 max-w-2xl mx-auto">
            如果你愿意体验一次"以能力为核心"的数字协作之旅，欢迎开始你的【合作画像】填写。
          </p>
          <Link href="/experience">
            <Button
              size="lg"
              className="bg-white text-[var(--coral-pink)] hover:bg-gray-100 font-semibold px-6 sm:px-8 py-3 text-base sm:text-lg"
            >
              👉 立即进入填写
            </Button>
          </Link>
        </section>
      </main>
    </div>
  )
}
