'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { ArrowLeft, ArrowRight } from 'lucide-react'
import Link from 'next/link'

// 导入步骤组件
import BasicInfoStep from '@/components/experience/BasicInfoStep'
import SkillsResourcesStep from '@/components/experience/SkillsResourcesStep'
import MBTIStep from '@/components/experience/MBTIStep'
import PreferencesStep from '@/components/experience/PreferencesStep'
import TimeMotivationStep from '@/components/experience/TimeMotivationStep'
import TagsStep from '@/components/experience/TagsStep'
import RadarStep from '@/components/experience/RadarStep'
import SummaryStep from '@/components/experience/SummaryStep'

const STEPS = [
  { id: 'basic', title: '基本信息', component: BasicInfoStep },
  { id: 'skills', title: '技能资源', component: SkillsResourcesStep },
  { id: 'mbti', title: 'MBTI测试', component: MBTIStep },
  { id: 'preferences', title: '合作偏好', component: PreferencesStep },
  { id: 'time', title: '时间动机', component: TimeMotivationStep },
  { id: 'tags', title: '标签画像', component: TagsStep },
  { id: 'radar', title: '能力评估', component: RadarStep },
  { id: 'summary', title: '信息确认', component: SummaryStep },
]

export default function ExperiencePage() {
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState({})

  const handleNext = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleStepData = (stepData: any) => {
    setFormData(prev => ({ ...prev, ...stepData }))
  }

  const CurrentStepComponent = STEPS[currentStep].component

  return (
    <div className="min-h-screen gradient-bg">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]">
              <ArrowLeft className="w-5 h-5 mr-2" />
              返回首页
            </Link>
            <div className="text-center">
              <h1 className="text-xl font-semibold text-[var(--dark-blue-gray)]">
                创建你的合作画像
              </h1>
              <p className="text-sm text-[var(--soft-gray)]">
                步骤 {currentStep + 1} / {STEPS.length}
              </p>
            </div>
            <div className="w-20"></div> {/* Spacer for centering */}
          </div>
        </div>
      </header>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-4">
            {STEPS.map((step, index) => (
              <div key={step.id} className="flex items-center flex-1">
                <div className="flex items-center">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      index <= currentStep
                        ? 'bg-[var(--coral-pink)] text-white'
                        : 'bg-[var(--mist-white)] text-[var(--soft-gray)]'
                    }`}
                  >
                    {index + 1}
                  </div>
                  <span
                    className={`ml-2 text-sm font-medium ${
                      index <= currentStep
                        ? 'text-[var(--dark-blue-gray)]'
                        : 'text-[var(--soft-gray)]'
                    }`}
                  >
                    {step.title}
                  </span>
                </div>
                {index < STEPS.length - 1 && (
                  <div
                    className={`flex-1 h-1 mx-4 rounded ${
                      index < currentStep
                        ? 'bg-[var(--coral-pink)]'
                        : 'bg-[var(--mist-white)]'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-xl card-shadow p-8">
          <CurrentStepComponent
            data={formData}
            onDataChange={handleStepData}
            onNext={handleNext}
            onPrev={handlePrev}
          />
        </div>

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={handlePrev}
            disabled={currentStep === 0}
            className="flex items-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            上一步
          </Button>
          
          <Button
            onClick={handleNext}
            disabled={currentStep === STEPS.length - 1}
            className="flex items-center"
          >
            下一步
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </main>
    </div>
  )
}
