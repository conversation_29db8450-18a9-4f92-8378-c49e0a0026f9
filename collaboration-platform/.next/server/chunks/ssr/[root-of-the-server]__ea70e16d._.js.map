{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs)\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/components/ui/button.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\nimport { ButtonHTMLAttributes, forwardRef } from 'react'\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          // Base styles\n          'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',\n          \n          // Size variants\n          {\n            'px-3 py-1.5 text-sm': size === 'sm',\n            'px-4 py-2 text-base': size === 'md',\n            'px-6 py-3 text-lg': size === 'lg',\n          },\n          \n          // Color variants\n          {\n            'bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)]': variant === 'primary',\n            'bg-[var(--mint-green)] text-white hover:bg-[#16a085] focus:ring-[var(--mint-green)]': variant === 'secondary',\n            'bg-[var(--brand-purple)] text-white hover:bg-[#7d3c98] focus:ring-[var(--brand-purple)]': variant === 'accent',\n            'border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]': variant === 'outline',\n            'text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)] focus:ring-[var(--soft-gray)]': variant === 'ghost',\n          },\n          \n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,GAAG,OAAO,EAAE;IAC1D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cAAc;QACd,mMAEA,gBAAgB;QAChB;YACE,uBAAuB,SAAS;YAChC,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GAEA,iBAAiB;QACjB;YACE,uFAAuF,YAAY;YACnG,uFAAuF,YAAY;YACnG,2FAA2F,YAAY;YACvG,6IAA6I,YAAY;YACzJ,2FAA2F,YAAY;QACzG,GAEA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Users, Target, Heart, Zap } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen gradient-bg\">\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-[var(--dark-blue-gray)] mb-6\">\n              找到你的\n              <span className=\"text-[var(--coral-pink)]\"> 完美合作伙伴</span>\n            </h1>\n            <p className=\"text-xl md:text-2xl text-[var(--soft-gray)] mb-8 max-w-3xl mx-auto\">\n              通过科学的性格分析和智能匹配算法，为你找到最适合的项目团队和合作伙伴\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Link href=\"/experience\">\n                <Button size=\"lg\" className=\"w-full sm:w-auto\">\n                  开始体验\n                </Button>\n              </Link>\n              <Link href=\"/about\">\n                <Button variant=\"outline\" size=\"lg\" className=\"w-full sm:w-auto\">\n                  了解更多\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-[var(--dark-blue-gray)] mb-4\">\n              为什么选择我们？\n            </h2>\n            <p className=\"text-lg text-[var(--soft-gray)] max-w-2xl mx-auto\">\n              基于科学的匹配算法，让合作更高效、更愉悦、更可信赖\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            <FeatureCard\n              icon={<Users className=\"w-8 h-8\" />}\n              title=\"精准匹配\"\n              description=\"基于MBTI性格测试和能力评估，为你匹配最合适的合作伙伴\"\n              color=\"var(--coral-pink)\"\n            />\n            <FeatureCard\n              icon={<Target className=\"w-8 h-8\" />}\n              title=\"科学评估\"\n              description=\"六维能力雷达图，全面展示个人协作画像\"\n              color=\"var(--mint-green)\"\n            />\n            <FeatureCard\n              icon={<Heart className=\"w-8 h-8\" />}\n              title=\"信任机制\"\n              description=\"透明的评价体系，建立可信赖的合作环境\"\n              color=\"var(--brand-purple)\"\n            />\n            <FeatureCard\n              icon={<Zap className=\"w-8 h-8\" />}\n              title=\"高效协作\"\n              description=\"智能推荐项目和团队，提升合作成功率\"\n              color=\"var(--ice-blue)\"\n            />\n          </div>\n        </div>\n      </section>\n\n      {/* Success Stories */}\n      <section className=\"py-20 gradient-bg\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-[var(--dark-blue-gray)] mb-4\">\n              成功案例\n            </h2>\n            <p className=\"text-lg text-[var(--soft-gray)]\">\n              已有数百个项目通过我们的平台找到了理想的合作伙伴\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <SuccessCard\n              title=\"AI学习平台\"\n              description=\"3位不同背景的创业者通过平台匹配，成功开发出个性化学习推荐系统\"\n              tags={['人工智能', '教育科技', '产品设计']}\n              result=\"已获得天使轮投资\"\n            />\n            <SuccessCard\n              title=\"环保社交应用\"\n              description=\"设计师与开发者的完美配合，打造了连接环保爱好者的社交平台\"\n              tags={['社交网络', '环保', '移动应用']}\n              result=\"用户突破10万\"\n            />\n            <SuccessCard\n              title=\"企业协作工具\"\n              description=\"技术专家与商业策划师的强强联合，开发出高效的团队协作解决方案\"\n              tags={['企业服务', '协作工具', 'SaaS']}\n              result=\"签约企业客户50+\"\n            />\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-[var(--coral-pink)]\">\n        <div className=\"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-6\">\n            准备好开始你的合作之旅了吗？\n          </h2>\n          <p className=\"text-xl text-white/90 mb-8\">\n            只需几分钟，就能创建你的专属合作画像\n          </p>\n          <Link href=\"/experience\">\n            <Button variant=\"secondary\" size=\"lg\">\n              立即开始体验\n            </Button>\n          </Link>\n        </div>\n      </section>\n    </div>\n  )\n}\n\ninterface FeatureCardProps {\n  icon: React.ReactNode\n  title: string\n  description: string\n  color: string\n}\n\nfunction FeatureCard({ icon, title, description, color }: FeatureCardProps) {\n  return (\n    <div className=\"text-center p-6 rounded-xl card-shadow card-hover bg-white\">\n      <div\n        className=\"w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\"\n        style={{ backgroundColor: `${color}20`, color }}\n      >\n        {icon}\n      </div>\n      <h3 className=\"text-xl font-semibold text-[var(--dark-blue-gray)] mb-2\">\n        {title}\n      </h3>\n      <p className=\"text-[var(--soft-gray)]\">\n        {description}\n      </p>\n    </div>\n  )\n}\n\ninterface SuccessCardProps {\n  title: string\n  description: string\n  tags: string[]\n  result: string\n}\n\nfunction SuccessCard({ title, description, tags, result }: SuccessCardProps) {\n  return (\n    <div className=\"bg-white p-6 rounded-xl card-shadow card-hover\">\n      <h3 className=\"text-xl font-semibold text-[var(--dark-blue-gray)] mb-3\">\n        {title}\n      </h3>\n      <p className=\"text-[var(--soft-gray)] mb-4\">\n        {description}\n      </p>\n      <div className=\"flex flex-wrap gap-2 mb-4\">\n        {tags.map((tag, index) => (\n          <span\n            key={index}\n            className=\"px-3 py-1 bg-[var(--mist-white)] text-[var(--dark-blue-gray)] text-sm rounded-full\"\n          >\n            {tag}\n          </span>\n        ))}\n      </div>\n      <div className=\"text-[var(--mint-green)] font-semibold\">\n        ✨ {result}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAmE;kDAE/E,8OAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;0CAE7C,8OAAC;gCAAE,WAAU;0CAAqE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAmB;;;;;;;;;;;kDAIjD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmE;;;;;;8CAGjF,8OAAC;oCAAE,WAAU;8CAAoD;;;;;;;;;;;;sCAKnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCACvB,OAAM;oCACN,aAAY;oCACZ,OAAM;;;;;;8CAER,8OAAC;oCACC,oBAAM,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACxB,OAAM;oCACN,aAAY;oCACZ,OAAM;;;;;;8CAER,8OAAC;oCACC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCACvB,OAAM;oCACN,aAAY;oCACZ,OAAM;;;;;;8CAER,8OAAC;oCACC,oBAAM,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCACrB,OAAM;oCACN,aAAY;oCACZ,OAAM;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmE;;;;;;8CAGjF,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAM;oCACN,aAAY;oCACZ,MAAM;wCAAC;wCAAQ;wCAAQ;qCAAO;oCAC9B,QAAO;;;;;;8CAET,8OAAC;oCACC,OAAM;oCACN,aAAY;oCACZ,MAAM;wCAAC;wCAAQ;wCAAM;qCAAO;oCAC5B,QAAO;;;;;;8CAET,8OAAC;oCACC,OAAM;oCACN,aAAY;oCACZ,MAAM;wCAAC;wCAAQ;wCAAQ;qCAAO;oCAC9B,QAAO;;;;;;;;;;;;;;;;;;;;;;;0BAOf,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;AASA,SAAS,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAoB;IACxE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;oBAAE;gBAAM;0BAE7C;;;;;;0BAEH,8OAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAIT;AASA,SAAS,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAoB;IACzE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,8OAAC;gBAAE,WAAU;0BACV;;;;;;0BAEH,8OAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC;wBAEC,WAAU;kCAET;uBAHI;;;;;;;;;;0BAOX,8OAAC;gBAAI,WAAU;;oBAAyC;oBACnD;;;;;;;;;;;;;AAIX", "debugId": null}}]}