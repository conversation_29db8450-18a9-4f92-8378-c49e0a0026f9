{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs)\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/components/ui/button.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\nimport { ButtonHTMLAttributes, forwardRef } from 'react'\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          // Base styles\n          'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',\n          \n          // Size variants\n          {\n            'px-3 py-1.5 text-sm': size === 'sm',\n            'px-4 py-2 text-base': size === 'md',\n            'px-6 py-3 text-lg': size === 'lg',\n          },\n          \n          // Color variants\n          {\n            'bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)]': variant === 'primary',\n            'bg-[var(--mint-green)] text-white hover:bg-[#16a085] focus:ring-[var(--mint-green)]': variant === 'secondary',\n            'bg-[var(--brand-purple)] text-white hover:bg-[#7d3c98] focus:ring-[var(--brand-purple)]': variant === 'accent',\n            'border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]': variant === 'outline',\n            'text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)] focus:ring-[var(--soft-gray)]': variant === 'ghost',\n          },\n          \n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,GAAG,OAAO,EAAE;IAC1D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;QACd,mMAEA,gBAAgB;QAChB;YACE,uBAAuB,SAAS;YAChC,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GAEA,iBAAiB;QACjB;YACE,uFAAuF,YAAY;YACnG,uFAAuF,YAAY;YACnG,2FAA2F,YAAY;YACvG,6IAA6I,YAAY;YACzJ,2FAA2F,YAAY;QACzG,GAEA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/data/mock-data.ts"], "sourcesContent": ["// 预设标签库\nexport const PRESET_TAGS = {\n  skills: [\n    '前端开发', '后端开发', '产品设计', '用户体验', '数据分析', \n    '市场营销', '商业策划', '项目管理', '内容创作', '视觉设计'\n  ],\n  personality: [\n    '团队协作', '独立思考', '创新思维', '执行力强', '沟通能力强',\n    '学习能力强', '抗压能力强', '领导力', '细心负责', '目标导向'\n  ],\n  workStyle: [\n    '远程工作', '线下协作', '灵活时间', '规律作息', '快速迭代',\n    '深度思考', '数据驱动', '用户导向', '技术驱动', '商业导向'\n  ],\n  interests: [\n    '人工智能', '区块链', '电商平台', '教育科技', '健康医疗',\n    '金融科技', '社交网络', '游戏娱乐', '企业服务', '生活服务'\n  ]\n}\n\n// MBTI类型\nexport const MBTI_TYPES = [\n  { code: 'INTJ', name: '建筑师', description: '富有想象力和战略性的思想家' },\n  { code: 'INTP', name: '思想家', description: '具有创造性的发明家' },\n  { code: 'ENTJ', name: '指挥官', description: '大胆、富有想象力、意志强烈的领导者' },\n  { code: 'ENTP', name: '辩论家', description: '聪明好奇的思想家' },\n  { code: 'INFJ', name: '提倡者', description: '安静而神秘的理想主义者' },\n  { code: 'INFP', name: '调停者', description: '诗意、善良的利他主义者' },\n  { code: 'ENFJ', name: '主人公', description: '富有魅力、鼓舞人心的领导者' },\n  { code: 'ENFP', name: '竞选者', description: '热情、有创造力的自由精神' },\n  { code: 'ISTJ', name: '物流师', description: '实用主义的事实导向者' },\n  { code: 'ISFJ', name: '守护者', description: '非常专注、温暖的守护者' },\n  { code: 'ESTJ', name: '总经理', description: '出色的管理者' },\n  { code: 'ESFJ', name: '执政官', description: '极有同情心、受欢迎的人' },\n  { code: 'ISTP', name: '鉴赏家', description: '大胆而实际的实验者' },\n  { code: 'ISFP', name: '探险家', description: '灵活、有魅力的艺术家' },\n  { code: 'ESTP', name: '企业家', description: '聪明、精力充沛的感知者' },\n  { code: 'ESFP', name: '娱乐家', description: '自发的、精力充沛的娱乐者' }\n]\n\n// 六边形雷达图维度\nexport const RADAR_DIMENSIONS = [\n  { key: 'communication', label: '沟通力', description: '表达和理解他人的能力' },\n  { key: 'execution', label: '执行力', description: '将想法转化为行动的能力' },\n  { key: 'empathy', label: '共情力', description: '理解和感受他人情感的能力' },\n  { key: 'analysis', label: '分析力', description: '逻辑思维和问题解决能力' },\n  { key: 'learning', label: '学习力', description: '快速掌握新知识和技能的能力' },\n  { key: 'leadership', label: '领导力', description: '影响和激励他人的能力' }\n]\n\n// 模拟用户数据\nexport const MOCK_USERS = [\n  {\n    id: '1',\n    name: '张小明',\n    age: 28,\n    location: '北京',\n    mbti: 'INTJ',\n    skills: ['前端开发', '产品设计'],\n    tags: ['团队协作', '创新思维', '远程工作'],\n    radarData: {\n      communication: 8,\n      execution: 9,\n      empathy: 6,\n      analysis: 9,\n      learning: 8,\n      leadership: 7\n    },\n    compatibility: 92\n  },\n  {\n    id: '2',\n    name: '李小红',\n    age: 25,\n    location: '上海',\n    mbti: 'ENFP',\n    skills: ['市场营销', '内容创作'],\n    tags: ['沟通能力强', '创新思维', '用户导向'],\n    radarData: {\n      communication: 9,\n      execution: 7,\n      empathy: 9,\n      analysis: 6,\n      learning: 8,\n      leadership: 8\n    },\n    compatibility: 87\n  },\n  {\n    id: '3',\n    name: '王小华',\n    age: 30,\n    location: '深圳',\n    mbti: 'ESTJ',\n    skills: ['项目管理', '商业策划'],\n    tags: ['执行力强', '领导力', '目标导向'],\n    radarData: {\n      communication: 8,\n      execution: 9,\n      empathy: 7,\n      analysis: 8,\n      learning: 7,\n      leadership: 9\n    },\n    compatibility: 85\n  }\n]\n\n// 模拟项目数据\nexport const MOCK_PROJECTS = [\n  {\n    id: '1',\n    title: 'AI驱动的学习平台',\n    description: '基于人工智能的个性化学习推荐系统',\n    tags: ['人工智能', '教育科技', '前端开发'],\n    teamSize: 4,\n    progress: 30,\n    compatibility: 94\n  },\n  {\n    id: '2',\n    title: '可持续生活社区',\n    description: '连接环保爱好者的社交平台',\n    tags: ['社交网络', '生活服务', '用户体验'],\n    teamSize: 3,\n    progress: 15,\n    compatibility: 89\n  }\n]\n"], "names": [], "mappings": "AAAA,QAAQ;;;;;;;;AACD,MAAM,cAAc;IACzB,QAAQ;QACN;QAAQ;QAAQ;QAAQ;QAAQ;QAChC;QAAQ;QAAQ;QAAQ;QAAQ;KACjC;IACD,aAAa;QACX;QAAQ;QAAQ;QAAQ;QAAQ;QAChC;QAAS;QAAS;QAAO;QAAQ;KAClC;IACD,WAAW;QACT;QAAQ;QAAQ;QAAQ;QAAQ;QAChC;QAAQ;QAAQ;QAAQ;QAAQ;KACjC;IACD,WAAW;QACT;QAAQ;QAAO;QAAQ;QAAQ;QAC/B;QAAQ;QAAQ;QAAQ;QAAQ;KACjC;AACH;AAGO,MAAM,aAAa;IACxB;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAgB;IAC1D;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAY;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAoB;IAC9D;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAW;IACrD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAc;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAc;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAgB;IAC1D;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAe;IACzD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAa;IACvD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAc;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAS;IACnD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAc;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAY;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAa;IACvD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAc;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAe;CAC1D;AAGM,MAAM,mBAAmB;IAC9B;QAAE,KAAK;QAAiB,OAAO;QAAO,aAAa;IAAa;IAChE;QAAE,KAAK;QAAa,OAAO;QAAO,aAAa;IAAc;IAC7D;QAAE,KAAK;QAAW,OAAO;QAAO,aAAa;IAAe;IAC5D;QAAE,KAAK;QAAY,OAAO;QAAO,aAAa;IAAc;IAC5D;QAAE,KAAK;QAAY,OAAO;QAAO,aAAa;IAAgB;IAC9D;QAAE,KAAK;QAAc,OAAO;QAAO,aAAa;IAAa;CAC9D;AAGM,MAAM,aAAa;IACxB;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;QACN,QAAQ;YAAC;YAAQ;SAAO;QACxB,MAAM;YAAC;YAAQ;YAAQ;SAAO;QAC9B,WAAW;YACT,eAAe;YACf,WAAW;YACX,SAAS;YACT,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;QACN,QAAQ;YAAC;YAAQ;SAAO;QACxB,MAAM;YAAC;YAAS;YAAQ;SAAO;QAC/B,WAAW;YACT,eAAe;YACf,WAAW;YACX,SAAS;YACT,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;QACN,QAAQ;YAAC;YAAQ;SAAO;QACxB,MAAM;YAAC;YAAQ;YAAO;SAAO;QAC7B,WAAW;YACT,eAAe;YACf,WAAW;YACX,SAAS;YACT,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,eAAe;IACjB;CACD;AAGM,MAAM,gBAAgB;IAC3B;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;YAAC;YAAQ;YAAQ;SAAO;QAC9B,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;YAAC;YAAQ;YAAQ;SAAO;QAC9B,UAAU;QACV,UAAU;QACV,eAAe;IACjB;CACD", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/app/profile/page.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { MBTI_TYPES, RADAR_DIMENSIONS, MOCK_USERS } from '@/data/mock-data'\nimport { User, Mail, MapPin, Edit, Share2, Settings } from 'lucide-react'\nimport { Radar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, ResponsiveContainer } from 'recharts'\nimport Link from 'next/link'\n\nexport default function ProfilePage() {\n  // 使用模拟数据\n  const user = MOCK_USERS[0]\n  const mbtiType = MBTI_TYPES.find(type => type.code === user.mbti)\n  \n  const radarChartData = RADAR_DIMENSIONS.map(dim => ({\n    dimension: dim.label,\n    value: user.radarData[dim.key],\n    fullMark: 10\n  }))\n\n  return (\n    <div className=\"min-h-screen gradient-bg\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link href=\"/\" className=\"text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]\">\n              ← 返回首页\n            </Link>\n            <h1 className=\"text-xl font-semibold text-[var(--dark-blue-gray)]\">\n              我的合作画像\n            </h1>\n            <div className=\"flex items-center space-x-2\">\n              <Button variant=\"outline\" size=\"sm\">\n                <Edit className=\"w-4 h-4 mr-2\" />\n                编辑\n              </Button>\n              <Button variant=\"outline\" size=\"sm\">\n                <Share2 className=\"w-4 h-4 mr-2\" />\n                分享\n              </Button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* 左侧：基本信息 */}\n          <div className=\"lg:col-span-1 space-y-6\">\n            {/* 个人信息卡片 */}\n            <div className=\"bg-white rounded-xl card-shadow p-6\">\n              <div className=\"text-center mb-6\">\n                <div className=\"w-20 h-20 bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <User className=\"w-10 h-10 text-white\" />\n                </div>\n                <h2 className=\"text-xl font-bold text-[var(--dark-blue-gray)]\">{user.name}</h2>\n                <p className=\"text-[var(--soft-gray)]\">{user.age}岁</p>\n              </div>\n              \n              <div className=\"space-y-3\">\n                <div className=\"flex items-center text-sm\">\n                  <MapPin className=\"w-4 h-4 text-[var(--soft-gray)] mr-2\" />\n                  <span>{user.location}</span>\n                </div>\n                <div className=\"flex items-center text-sm\">\n                  <Mail className=\"w-4 h-4 text-[var(--soft-gray)] mr-2\" />\n                  <span><EMAIL></span>\n                </div>\n              </div>\n            </div>\n\n            {/* MBTI卡片 */}\n            {mbtiType && (\n              <div className=\"bg-gradient-to-r from-[var(--brand-purple)] to-[var(--ice-blue)] rounded-xl p-6 text-white\">\n                <h3 className=\"text-lg font-semibold mb-4\">MBTI性格类型</h3>\n                <div className=\"flex items-center mb-3\">\n                  <div className=\"text-3xl font-bold mr-4\">{mbtiType.code}</div>\n                  <div>\n                    <div className=\"font-semibold\">{mbtiType.name}</div>\n                    <div className=\"text-sm opacity-90\">{mbtiType.description}</div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* 技能标签 */}\n            <div className=\"bg-white rounded-xl card-shadow p-6\">\n              <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">核心技能</h3>\n              <div className=\"flex flex-wrap gap-2\">\n                {user.skills.map((skill, index) => (\n                  <span \n                    key={index}\n                    className=\"px-3 py-1 bg-[var(--coral-pink)] text-white text-sm rounded-full\"\n                  >\n                    {skill}\n                  </span>\n                ))}\n              </div>\n            </div>\n\n            {/* 个性标签 */}\n            <div className=\"bg-white rounded-xl card-shadow p-6\">\n              <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">个性标签</h3>\n              <div className=\"flex flex-wrap gap-2\">\n                {user.tags.map((tag, index) => (\n                  <span \n                    key={index}\n                    className=\"px-3 py-1 bg-[var(--mint-green)] text-white text-sm rounded-full\"\n                  >\n                    {tag}\n                  </span>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* 右侧：能力雷达图和详细信息 */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* 能力雷达图 */}\n            <div className=\"bg-white rounded-xl card-shadow p-6\">\n              <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-6\">六维能力评估</h3>\n              <div className=\"grid lg:grid-cols-2 gap-8\">\n                <div className=\"h-80\">\n                  <ResponsiveContainer width=\"100%\" height=\"100%\">\n                    <RadarChart data={radarChartData}>\n                      <PolarGrid />\n                      <PolarAngleAxis \n                        dataKey=\"dimension\" \n                        tick={{ fontSize: 12, fill: '#7F8C8D' }}\n                      />\n                      <PolarRadiusAxis \n                        angle={90} \n                        domain={[0, 10]} \n                        tick={{ fontSize: 10, fill: '#BDC3C7' }}\n                      />\n                      <Radar\n                        name=\"能力值\"\n                        dataKey=\"value\"\n                        stroke=\"#FF6B6B\"\n                        fill=\"#FF6B6B\"\n                        fillOpacity={0.3}\n                        strokeWidth={2}\n                      />\n                    </RadarChart>\n                  </ResponsiveContainer>\n                </div>\n                \n                <div className=\"space-y-4\">\n                  {RADAR_DIMENSIONS.map((dim) => (\n                    <div key={dim.key} className=\"flex items-center justify-between\">\n                      <div>\n                        <div className=\"font-medium text-[var(--dark-blue-gray)]\">{dim.label}</div>\n                        <div className=\"text-sm text-[var(--soft-gray)]\">{dim.description}</div>\n                      </div>\n                      <div className=\"text-2xl font-bold text-[var(--coral-pink)]\">\n                        {user.radarData[dim.key]}\n                      </div>\n                    </div>\n                  ))}\n                  \n                  <div className=\"pt-4 border-t\">\n                    <div className=\"text-center\">\n                      <div className=\"text-sm text-[var(--soft-gray)]\">综合评分</div>\n                      <div className=\"text-3xl font-bold text-[var(--coral-pink)]\">\n                        {(Object.values(user.radarData).reduce((sum, score) => sum + score, 0) / 6).toFixed(1)}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* 合作偏好 */}\n            <div className=\"bg-white rounded-xl card-shadow p-6\">\n              <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-6\">合作偏好</h3>\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                <div>\n                  <h4 className=\"font-medium text-[var(--dark-blue-gray)] mb-3\">工作方式</h4>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3\"></div>\n                      <span className=\"text-sm\">远程工作</span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <div className=\"w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3\"></div>\n                      <span className=\"text-sm\">灵活时间</span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <div className=\"w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3\"></div>\n                      <span className=\"text-sm\">深度思考</span>\n                    </div>\n                  </div>\n                </div>\n                \n                <div>\n                  <h4 className=\"font-medium text-[var(--dark-blue-gray)] mb-3\">团队偏好</h4>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3\"></div>\n                      <span className=\"text-sm\">2-3人小团队</span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <div className=\"w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3\"></div>\n                      <span className=\"text-sm\">技术导向</span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <div className=\"w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3\"></div>\n                      <span className=\"text-sm\">中度承诺</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* 行动按钮 */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <Link href=\"/match\" className=\"flex-1\">\n                <Button size=\"lg\" className=\"w-full\">\n                  查看匹配推荐\n                </Button>\n              </Link>\n              <Link href=\"/experience\" className=\"flex-1\">\n                <Button variant=\"outline\" size=\"lg\" className=\"w-full\">\n                  更新画像信息\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,SAAS;IACT,MAAM,OAAO,8HAAA,CAAA,aAAU,CAAC,EAAE;IAC1B,MAAM,WAAW,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,KAAK,IAAI;IAEhE,MAAM,iBAAiB,8HAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;YAClD,WAAW,IAAI,KAAK;YACpB,OAAO,KAAK,SAAS,CAAC,IAAI,GAAG,CAAC;YAC9B,UAAU;QACZ,CAAC;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAA8D;;;;;;0CAGvF,6LAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,6LAAC,8MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;oDAAG,WAAU;8DAAkD,KAAK,IAAI;;;;;;8DACzE,6LAAC;oDAAE,WAAU;;wDAA2B,KAAK,GAAG;wDAAC;;;;;;;;;;;;;sDAGnD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;sEAAM,KAAK,QAAQ;;;;;;;;;;;;8DAEtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;gCAMX,0BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA2B,SAAS,IAAI;;;;;;8DACvD,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAiB,SAAS,IAAI;;;;;;sEAC7C,6LAAC;4DAAI,WAAU;sEAAsB,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;8CAOjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0D;;;;;;sDACxE,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;;8CAUb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0D;;;;;;sDACxE,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;;;;;;;;sCAWf,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0D;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;wDAAC,OAAM;wDAAO,QAAO;kEACvC,cAAA,6LAAC,yJAAA,CAAA,aAAU;4DAAC,MAAM;;8EAChB,6LAAC,wJAAA,CAAA,YAAS;;;;;8EACV,6LAAC,6JAAA,CAAA,iBAAc;oEACb,SAAQ;oEACR,MAAM;wEAAE,UAAU;wEAAI,MAAM;oEAAU;;;;;;8EAExC,6LAAC,8JAAA,CAAA,kBAAe;oEACd,OAAO;oEACP,QAAQ;wEAAC;wEAAG;qEAAG;oEACf,MAAM;wEAAE,UAAU;wEAAI,MAAM;oEAAU;;;;;;8EAExC,6LAAC,oJAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,SAAQ;oEACR,QAAO;oEACP,MAAK;oEACL,aAAa;oEACb,aAAa;;;;;;;;;;;;;;;;;;;;;;8DAMrB,6LAAC;oDAAI,WAAU;;wDACZ,8HAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,oBACrB,6LAAC;gEAAkB,WAAU;;kFAC3B,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FAA4C,IAAI,KAAK;;;;;;0FACpE,6LAAC;gFAAI,WAAU;0FAAmC,IAAI,WAAW;;;;;;;;;;;;kFAEnE,6LAAC;wEAAI,WAAU;kFACZ,KAAK,SAAS,CAAC,IAAI,GAAG,CAAC;;;;;;;+DANlB,IAAI,GAAG;;;;;sEAWnB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAkC;;;;;;kFACjD,6LAAC;wEAAI,WAAU;kFACZ,CAAC,OAAO,MAAM,CAAC,KAAK,SAAS,EAAE,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAShG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0D;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgD;;;;;;sEAC9D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;8EAE5B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;8EAE5B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;;;;;;;;8DAKhC,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgD;;;;;;sEAC9D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;8EAE5B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;8EAE5B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAC5B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;sDAIvC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;sDACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvE;KAhOwB", "debugId": null}}]}