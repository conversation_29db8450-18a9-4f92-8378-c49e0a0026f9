{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs)\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/components/ui/button.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\nimport { ButtonHTMLAttributes, forwardRef } from 'react'\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg'\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          // Base styles\n          'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',\n          \n          // Size variants\n          {\n            'px-3 py-1.5 text-sm': size === 'sm',\n            'px-4 py-2 text-base': size === 'md',\n            'px-6 py-3 text-lg': size === 'lg',\n          },\n          \n          // Color variants\n          {\n            'bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)]': variant === 'primary',\n            'bg-[var(--mint-green)] text-white hover:bg-[#16a085] focus:ring-[var(--mint-green)]': variant === 'secondary',\n            'bg-[var(--brand-purple)] text-white hover:bg-[#7d3c98] focus:ring-[var(--brand-purple)]': variant === 'accent',\n            'border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]': variant === 'outline',\n            'text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)] focus:ring-[var(--soft-gray)]': variant === 'ghost',\n          },\n          \n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,GAAG,OAAO,EAAE;IAC1D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;QACd,mMAEA,gBAAgB;QAChB;YACE,uBAAuB,SAAS;YAChC,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;QAChC,GAEA,iBAAiB;QACjB;YACE,uFAAuF,YAAY;YACnG,uFAAuF,YAAY;YACnG,2FAA2F,YAAY;YACvG,6IAA6I,YAAY;YACzJ,2FAA2F,YAAY;QACzG,GAEA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/components/experience/BasicInfoStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\n\ninterface BasicInfoStepProps {\n  data: any\n  onDataChange: (data: any) => void\n  onNext: () => void\n  onPrev: () => void\n}\n\nexport default function BasicInfoStep({ data, onDataChange, onNext }: BasicInfoStepProps) {\n  const [formData, setFormData] = useState({\n    name: data.name || '',\n    age: data.age || '',\n    gender: data.gender || '',\n    location: data.location || '',\n    email: data.email || '',\n    phone: data.phone || ''\n  })\n\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  useEffect(() => {\n    onDataChange(formData)\n  }, [formData, onDataChange])\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }))\n    }\n  }\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n    \n    if (!formData.name.trim()) {\n      newErrors.name = '请输入姓名'\n    }\n    \n    if (!formData.age) {\n      newErrors.age = '请选择年龄段'\n    }\n    \n    if (!formData.gender) {\n      newErrors.gender = '请选择性别'\n    }\n    \n    if (!formData.location.trim()) {\n      newErrors.location = '请输入所在地区'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleNext = () => {\n    if (validateForm()) {\n      onNext()\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2\">\n          基本信息\n        </h2>\n        <p className=\"text-[var(--soft-gray)]\">\n          让我们先了解一下你的基本情况\n        </p>\n      </div>\n\n      <div className=\"grid md:grid-cols-2 gap-6\">\n        {/* 姓名 */}\n        <div>\n          <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n            姓名 *\n          </label>\n          <input\n            type=\"text\"\n            value={formData.name}\n            onChange={(e) => handleInputChange('name', e.target.value)}\n            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent ${\n              errors.name ? 'border-red-500' : 'border-[var(--soft-gray)]'\n            }`}\n            placeholder=\"请输入你的姓名\"\n          />\n          {errors.name && (\n            <p className=\"mt-1 text-sm text-red-500\">{errors.name}</p>\n          )}\n        </div>\n\n        {/* 年龄段 */}\n        <div>\n          <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n            年龄段 *\n          </label>\n          <select\n            value={formData.age}\n            onChange={(e) => handleInputChange('age', e.target.value)}\n            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent ${\n              errors.age ? 'border-red-500' : 'border-[var(--soft-gray)]'\n            }`}\n          >\n            <option value=\"\">请选择年龄段</option>\n            <option value=\"18-22\">18-22岁</option>\n            <option value=\"23-27\">23-27岁</option>\n            <option value=\"28-32\">28-32岁</option>\n            <option value=\"33-37\">33-37岁</option>\n            <option value=\"38-42\">38-42岁</option>\n            <option value=\"43+\">43岁以上</option>\n          </select>\n          {errors.age && (\n            <p className=\"mt-1 text-sm text-red-500\">{errors.age}</p>\n          )}\n        </div>\n\n        {/* 性别 */}\n        <div>\n          <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n            性别 *\n          </label>\n          <div className=\"flex space-x-4\">\n            {['男', '女', '其他'].map((option) => (\n              <label key={option} className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"gender\"\n                  value={option}\n                  checked={formData.gender === option}\n                  onChange={(e) => handleInputChange('gender', e.target.value)}\n                  className=\"mr-2 text-[var(--coral-pink)] focus:ring-[var(--coral-pink)]\"\n                />\n                <span className=\"text-[var(--dark-blue-gray)]\">{option}</span>\n              </label>\n            ))}\n          </div>\n          {errors.gender && (\n            <p className=\"mt-1 text-sm text-red-500\">{errors.gender}</p>\n          )}\n        </div>\n\n        {/* 所在地区 */}\n        <div>\n          <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n            所在地区 *\n          </label>\n          <input\n            type=\"text\"\n            value={formData.location}\n            onChange={(e) => handleInputChange('location', e.target.value)}\n            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent ${\n              errors.location ? 'border-red-500' : 'border-[var(--soft-gray)]'\n            }`}\n            placeholder=\"如：北京、上海、深圳\"\n          />\n          {errors.location && (\n            <p className=\"mt-1 text-sm text-red-500\">{errors.location}</p>\n          )}\n        </div>\n\n        {/* 邮箱（可选） */}\n        <div>\n          <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n            邮箱（可选）\n          </label>\n          <input\n            type=\"email\"\n            value={formData.email}\n            onChange={(e) => handleInputChange('email', e.target.value)}\n            className=\"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent\"\n            placeholder=\"<EMAIL>\"\n          />\n        </div>\n\n        {/* 手机号（可选） */}\n        <div>\n          <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n            手机号（可选）\n          </label>\n          <input\n            type=\"tel\"\n            value={formData.phone}\n            onChange={(e) => handleInputChange('phone', e.target.value)}\n            className=\"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent\"\n            placeholder=\"手机号码\"\n          />\n        </div>\n      </div>\n\n      <div className=\"flex justify-end mt-8\">\n        <Button onClick={handleNext} size=\"lg\">\n          下一步：技能资源\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAYe,SAAS,cAAc,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAsB;;IACtF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM,KAAK,IAAI,IAAI;QACnB,KAAK,KAAK,GAAG,IAAI;QACjB,QAAQ,KAAK,MAAM,IAAI;QACvB,UAAU,KAAK,QAAQ,IAAI;QAC3B,OAAO,KAAK,KAAK,IAAI;QACrB,OAAO,KAAK,KAAK,IAAI;IACvB;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,aAAa;QACf;kCAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,GAAG,EAAE;YACjB,UAAU,GAAG,GAAG;QAClB;QAEA,IAAI,CAAC,SAAS,MAAM,EAAE;YACpB,UAAU,MAAM,GAAG;QACrB;QAEA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,UAAU,QAAQ,GAAG;QACvB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB;YAClB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA8D;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACzD,WAAW,CAAC,wGAAwG,EAClH,OAAO,IAAI,GAAG,mBAAmB,6BACjC;gCACF,aAAY;;;;;;4BAEb,OAAO,IAAI,kBACV,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,IAAI;;;;;;;;;;;;kCAKzD,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA8D;;;;;;0CAG/E,6LAAC;gCACC,OAAO,SAAS,GAAG;gCACnB,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;gCACxD,WAAW,CAAC,wGAAwG,EAClH,OAAO,GAAG,GAAG,mBAAmB,6BAChC;;kDAEF,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;;4BAErB,OAAO,GAAG,kBACT,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,GAAG;;;;;;;;;;;;kCAKxD,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA8D;;;;;;0CAG/E,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAK;oCAAK;iCAAK,CAAC,GAAG,CAAC,CAAC,uBACrB,6LAAC;wCAAmB,WAAU;;0DAC5B,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO;gDACP,SAAS,SAAS,MAAM,KAAK;gDAC7B,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;gDAC3D,WAAU;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;uCATtC;;;;;;;;;;4BAaf,OAAO,MAAM,kBACZ,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,MAAM;;;;;;;;;;;;kCAK3D,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA8D;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC7D,WAAW,CAAC,wGAAwG,EAClH,OAAO,QAAQ,GAAG,mBAAmB,6BACrC;gCACF,aAAY;;;;;;4BAEb,OAAO,QAAQ,kBACd,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ;;;;;;;;;;;;kCAK7D,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA8D;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gCAC1D,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAKhB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA8D;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gCAC1D,WAAU;gCACV,aAAY;;;;;;;;;;;;;;;;;;0BAKlB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAY,MAAK;8BAAK;;;;;;;;;;;;;;;;;AAM/C;GA7LwB;KAAA", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/data/mock-data.ts"], "sourcesContent": ["// 预设标签库\nexport const PRESET_TAGS = {\n  skills: [\n    '前端开发', '后端开发', '产品设计', '用户体验', '数据分析', \n    '市场营销', '商业策划', '项目管理', '内容创作', '视觉设计'\n  ],\n  personality: [\n    '团队协作', '独立思考', '创新思维', '执行力强', '沟通能力强',\n    '学习能力强', '抗压能力强', '领导力', '细心负责', '目标导向'\n  ],\n  workStyle: [\n    '远程工作', '线下协作', '灵活时间', '规律作息', '快速迭代',\n    '深度思考', '数据驱动', '用户导向', '技术驱动', '商业导向'\n  ],\n  interests: [\n    '人工智能', '区块链', '电商平台', '教育科技', '健康医疗',\n    '金融科技', '社交网络', '游戏娱乐', '企业服务', '生活服务'\n  ]\n}\n\n// MBTI类型\nexport const MBTI_TYPES = [\n  { code: 'INTJ', name: '建筑师', description: '富有想象力和战略性的思想家' },\n  { code: 'INTP', name: '思想家', description: '具有创造性的发明家' },\n  { code: 'ENTJ', name: '指挥官', description: '大胆、富有想象力、意志强烈的领导者' },\n  { code: 'ENTP', name: '辩论家', description: '聪明好奇的思想家' },\n  { code: 'INFJ', name: '提倡者', description: '安静而神秘的理想主义者' },\n  { code: 'INFP', name: '调停者', description: '诗意、善良的利他主义者' },\n  { code: 'ENFJ', name: '主人公', description: '富有魅力、鼓舞人心的领导者' },\n  { code: 'ENFP', name: '竞选者', description: '热情、有创造力的自由精神' },\n  { code: 'ISTJ', name: '物流师', description: '实用主义的事实导向者' },\n  { code: 'ISFJ', name: '守护者', description: '非常专注、温暖的守护者' },\n  { code: 'ESTJ', name: '总经理', description: '出色的管理者' },\n  { code: 'ESFJ', name: '执政官', description: '极有同情心、受欢迎的人' },\n  { code: 'ISTP', name: '鉴赏家', description: '大胆而实际的实验者' },\n  { code: 'ISFP', name: '探险家', description: '灵活、有魅力的艺术家' },\n  { code: 'ESTP', name: '企业家', description: '聪明、精力充沛的感知者' },\n  { code: 'ESFP', name: '娱乐家', description: '自发的、精力充沛的娱乐者' }\n]\n\n// 六边形雷达图维度\nexport const RADAR_DIMENSIONS = [\n  { key: 'communication', label: '沟通力', description: '表达和理解他人的能力' },\n  { key: 'execution', label: '执行力', description: '将想法转化为行动的能力' },\n  { key: 'empathy', label: '共情力', description: '理解和感受他人情感的能力' },\n  { key: 'analysis', label: '分析力', description: '逻辑思维和问题解决能力' },\n  { key: 'learning', label: '学习力', description: '快速掌握新知识和技能的能力' },\n  { key: 'leadership', label: '领导力', description: '影响和激励他人的能力' }\n]\n\n// 模拟用户数据\nexport const MOCK_USERS = [\n  {\n    id: '1',\n    name: '张小明',\n    age: 28,\n    location: '北京',\n    mbti: 'INTJ',\n    skills: ['前端开发', '产品设计'],\n    tags: ['团队协作', '创新思维', '远程工作'],\n    radarData: {\n      communication: 8,\n      execution: 9,\n      empathy: 6,\n      analysis: 9,\n      learning: 8,\n      leadership: 7\n    },\n    compatibility: 92\n  },\n  {\n    id: '2',\n    name: '李小红',\n    age: 25,\n    location: '上海',\n    mbti: 'ENFP',\n    skills: ['市场营销', '内容创作'],\n    tags: ['沟通能力强', '创新思维', '用户导向'],\n    radarData: {\n      communication: 9,\n      execution: 7,\n      empathy: 9,\n      analysis: 6,\n      learning: 8,\n      leadership: 8\n    },\n    compatibility: 87\n  },\n  {\n    id: '3',\n    name: '王小华',\n    age: 30,\n    location: '深圳',\n    mbti: 'ESTJ',\n    skills: ['项目管理', '商业策划'],\n    tags: ['执行力强', '领导力', '目标导向'],\n    radarData: {\n      communication: 8,\n      execution: 9,\n      empathy: 7,\n      analysis: 8,\n      learning: 7,\n      leadership: 9\n    },\n    compatibility: 85\n  }\n]\n\n// 模拟项目数据\nexport const MOCK_PROJECTS = [\n  {\n    id: '1',\n    title: 'AI驱动的学习平台',\n    description: '基于人工智能的个性化学习推荐系统',\n    tags: ['人工智能', '教育科技', '前端开发'],\n    teamSize: 4,\n    progress: 30,\n    compatibility: 94\n  },\n  {\n    id: '2',\n    title: '可持续生活社区',\n    description: '连接环保爱好者的社交平台',\n    tags: ['社交网络', '生活服务', '用户体验'],\n    teamSize: 3,\n    progress: 15,\n    compatibility: 89\n  }\n]\n"], "names": [], "mappings": "AAAA,QAAQ;;;;;;;;AACD,MAAM,cAAc;IACzB,QAAQ;QACN;QAAQ;QAAQ;QAAQ;QAAQ;QAChC;QAAQ;QAAQ;QAAQ;QAAQ;KACjC;IACD,aAAa;QACX;QAAQ;QAAQ;QAAQ;QAAQ;QAChC;QAAS;QAAS;QAAO;QAAQ;KAClC;IACD,WAAW;QACT;QAAQ;QAAQ;QAAQ;QAAQ;QAChC;QAAQ;QAAQ;QAAQ;QAAQ;KACjC;IACD,WAAW;QACT;QAAQ;QAAO;QAAQ;QAAQ;QAC/B;QAAQ;QAAQ;QAAQ;QAAQ;KACjC;AACH;AAGO,MAAM,aAAa;IACxB;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAgB;IAC1D;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAY;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAoB;IAC9D;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAW;IACrD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAc;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAc;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAgB;IAC1D;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAe;IACzD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAa;IACvD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAc;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAS;IACnD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAc;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAY;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAa;IACvD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAc;IACxD;QAAE,MAAM;QAAQ,MAAM;QAAO,aAAa;IAAe;CAC1D;AAGM,MAAM,mBAAmB;IAC9B;QAAE,KAAK;QAAiB,OAAO;QAAO,aAAa;IAAa;IAChE;QAAE,KAAK;QAAa,OAAO;QAAO,aAAa;IAAc;IAC7D;QAAE,KAAK;QAAW,OAAO;QAAO,aAAa;IAAe;IAC5D;QAAE,KAAK;QAAY,OAAO;QAAO,aAAa;IAAc;IAC5D;QAAE,KAAK;QAAY,OAAO;QAAO,aAAa;IAAgB;IAC9D;QAAE,KAAK;QAAc,OAAO;QAAO,aAAa;IAAa;CAC9D;AAGM,MAAM,aAAa;IACxB;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;QACN,QAAQ;YAAC;YAAQ;SAAO;QACxB,MAAM;YAAC;YAAQ;YAAQ;SAAO;QAC9B,WAAW;YACT,eAAe;YACf,WAAW;YACX,SAAS;YACT,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;QACN,QAAQ;YAAC;YAAQ;SAAO;QACxB,MAAM;YAAC;YAAS;YAAQ;SAAO;QAC/B,WAAW;YACT,eAAe;YACf,WAAW;YACX,SAAS;YACT,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,UAAU;QACV,MAAM;QACN,QAAQ;YAAC;YAAQ;SAAO;QACxB,MAAM;YAAC;YAAQ;YAAO;SAAO;QAC7B,WAAW;YACT,eAAe;YACf,WAAW;YACX,SAAS;YACT,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,eAAe;IACjB;CACD;AAGM,MAAM,gBAAgB;IAC3B;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;YAAC;YAAQ;YAAQ;SAAO;QAC9B,UAAU;QACV,UAAU;QACV,eAAe;IACjB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;YAAC;YAAQ;YAAQ;SAAO;QAC9B,UAAU;QACV,UAAU;QACV,eAAe;IACjB;CACD", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/components/experience/SkillsResourcesStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { PRESET_TAGS } from '@/data/mock-data'\nimport { Plus, X } from 'lucide-react'\n\ninterface SkillsResourcesStepProps {\n  data: any\n  onDataChange: (data: any) => void\n  onNext: () => void\n  onPrev: () => void\n}\n\nexport default function SkillsResourcesStep({ data, onDataChange, onNext }: SkillsResourcesStepProps) {\n  const [formData, setFormData] = useState({\n    skills: data.skills || [],\n    resources: data.resources || [],\n    ideas: data.ideas || '',\n    funding: data.funding || '',\n    customSkill: '',\n    customResource: ''\n  })\n\n  useEffect(() => {\n    onDataChange(formData)\n  }, [formData, onDataChange])\n\n  const handleSkillToggle = (skill: string) => {\n    setFormData(prev => ({\n      ...prev,\n      skills: prev.skills.includes(skill)\n        ? prev.skills.filter((s: string) => s !== skill)\n        : [...prev.skills, skill]\n    }))\n  }\n\n  const handleAddCustomSkill = () => {\n    if (formData.customSkill.trim() && !formData.skills.includes(formData.customSkill.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        skills: [...prev.skills, prev.customSkill.trim()],\n        customSkill: ''\n      }))\n    }\n  }\n\n  const handleResourceChange = (type: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      resources: {\n        ...prev.resources,\n        [type]: value\n      }\n    }))\n  }\n\n  const removeSkill = (skill: string) => {\n    setFormData(prev => ({\n      ...prev,\n      skills: prev.skills.filter((s: string) => s !== skill)\n    }))\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2\">\n          技能与资源\n        </h2>\n        <p className=\"text-[var(--soft-gray)]\">\n          告诉我们你拥有的技能和可以提供的资源\n        </p>\n      </div>\n\n      {/* 技能选择 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          你的技能专长\n        </h3>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mb-4\">\n          {PRESET_TAGS.skills.map((skill) => (\n            <button\n              key={skill}\n              onClick={() => handleSkillToggle(skill)}\n              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${\n                formData.skills.includes(skill)\n                  ? 'bg-[var(--coral-pink)] text-white'\n                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white'\n              }`}\n            >\n              {skill}\n            </button>\n          ))}\n        </div>\n\n        {/* 自定义技能 */}\n        <div className=\"flex gap-2\">\n          <input\n            type=\"text\"\n            value={formData.customSkill}\n            onChange={(e) => setFormData(prev => ({ ...prev, customSkill: e.target.value }))}\n            placeholder=\"添加其他技能...\"\n            className=\"flex-1 px-4 py-2 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent\"\n            onKeyPress={(e) => e.key === 'Enter' && handleAddCustomSkill()}\n          />\n          <Button onClick={handleAddCustomSkill} variant=\"outline\" size=\"sm\">\n            <Plus className=\"w-4 h-4\" />\n          </Button>\n        </div>\n\n        {/* 已选技能 */}\n        {formData.skills.length > 0 && (\n          <div className=\"mt-4\">\n            <p className=\"text-sm text-[var(--soft-gray)] mb-2\">已选择的技能：</p>\n            <div className=\"flex flex-wrap gap-2\">\n              {formData.skills.map((skill: string) => (\n                <span\n                  key={skill}\n                  className=\"inline-flex items-center px-3 py-1 bg-[var(--coral-pink)] text-white text-sm rounded-full\"\n                >\n                  {skill}\n                  <button\n                    onClick={() => removeSkill(skill)}\n                    className=\"ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-0.5\"\n                  >\n                    <X className=\"w-3 h-3\" />\n                  </button>\n                </span>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 资源类型 */}\n      <div className=\"grid md:grid-cols-2 gap-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n            创意想法\n          </label>\n          <textarea\n            value={formData.ideas}\n            onChange={(e) => setFormData(prev => ({ ...prev, ideas: e.target.value }))}\n            rows={4}\n            className=\"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent\"\n            placeholder=\"描述你的项目想法或创意概念...\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n            资金情况\n          </label>\n          <select\n            value={formData.funding}\n            onChange={(e) => setFormData(prev => ({ ...prev, funding: e.target.value }))}\n            className=\"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent\"\n          >\n            <option value=\"\">选择资金情况</option>\n            <option value=\"none\">暂无资金</option>\n            <option value=\"small\">少量资金（1-5万）</option>\n            <option value=\"medium\">中等资金（5-20万）</option>\n            <option value=\"large\">充足资金（20万以上）</option>\n            <option value=\"seeking\">正在寻求投资</option>\n          </select>\n        </div>\n      </div>\n\n      {/* 其他资源 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          其他可提供的资源\n        </h3>\n        <div className=\"grid md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n              人脉网络\n            </label>\n            <input\n              type=\"text\"\n              value={formData.resources.network || ''}\n              onChange={(e) => handleResourceChange('network', e.target.value)}\n              className=\"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent\"\n              placeholder=\"如：投资人、行业专家、技术大牛等\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n              设备工具\n            </label>\n            <input\n              type=\"text\"\n              value={formData.resources.equipment || ''}\n              onChange={(e) => handleResourceChange('equipment', e.target.value)}\n              className=\"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent\"\n              placeholder=\"如：服务器、设计软件、办公场地等\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex justify-end mt-8\">\n        <Button onClick={onNext} size=\"lg\">\n          下一步：MBTI测试\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AAce,SAAS,oBAAoB,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAA4B;;IAClG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,QAAQ,KAAK,MAAM,IAAI,EAAE;QACzB,WAAW,KAAK,SAAS,IAAI,EAAE;QAC/B,OAAO,KAAK,KAAK,IAAI;QACrB,SAAS,KAAK,OAAO,IAAI;QACzB,aAAa;QACb,gBAAgB;IAClB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,aAAa;QACf;wCAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,SACzB,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,IAAc,MAAM,SACxC;uBAAI,KAAK,MAAM;oBAAE;iBAAM;YAC7B,CAAC;IACH;IAEA,MAAM,uBAAuB;QAC3B,IAAI,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,SAAS,WAAW,CAAC,IAAI,KAAK;YACzF,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,QAAQ;2BAAI,KAAK,MAAM;wBAAE,KAAK,WAAW,CAAC,IAAI;qBAAG;oBACjD,aAAa;gBACf,CAAC;QACH;IACF;IAEA,MAAM,uBAAuB,CAAC,MAAc;QAC1C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW;oBACT,GAAG,KAAK,SAAS;oBACjB,CAAC,KAAK,EAAE;gBACV;YACF,CAAC;IACH;IAEA,MAAM,cAAc,CAAC;QACnB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,IAAc,MAAM;YAClD,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAMzC,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;kCACZ,8HAAA,CAAA,cAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,sBACvB,6LAAC;gCAEC,SAAS,IAAM,kBAAkB;gCACjC,WAAW,CAAC,wDAAwD,EAClE,SAAS,MAAM,CAAC,QAAQ,CAAC,SACrB,sCACA,qGACJ;0CAED;+BARI;;;;;;;;;;kCAcX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,OAAO,SAAS,WAAW;gCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAC9E,aAAY;gCACZ,WAAU;gCACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;0CAE1C,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAsB,SAAQ;gCAAU,MAAK;0CAC5D,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAKnB,SAAS,MAAM,CAAC,MAAM,GAAG,mBACxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CACpD,6LAAC;gCAAI,WAAU;0CACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,sBACpB,6LAAC;wCAEC,WAAU;;4CAET;0DACD,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCARV;;;;;;;;;;;;;;;;;;;;;;0BAkBjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA8D;;;;;;0CAG/E,6LAAC;gCACC,OAAO,SAAS,KAAK;gCACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACxE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA8D;;;;;;0CAG/E,6LAAC;gCACC,OAAO,SAAS,OAAO;gCACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAC1E,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAM9B,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA8D;;;;;;kDAG/E,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,SAAS,CAAC,OAAO,IAAI;wCACrC,UAAU,CAAC,IAAM,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC/D,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAGhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA8D;;;;;;kDAG/E,6LAAC;wCACC,MAAK;wCACL,OAAO,SAAS,SAAS,CAAC,SAAS,IAAI;wCACvC,UAAU,CAAC,IAAM,qBAAqB,aAAa,EAAE,MAAM,CAAC,KAAK;wCACjE,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAMpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAQ,MAAK;8BAAK;;;;;;;;;;;;;;;;;AAM3C;GAnMwB;KAAA", "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/components/experience/MBTIStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { MBTI_TYPES } from '@/data/mock-data'\nimport { ExternalLink, Brain, CheckCircle } from 'lucide-react'\n\ninterface MBTIStepProps {\n  data: any\n  onDataChange: (data: any) => void\n  onNext: () => void\n  onPrev: () => void\n}\n\nexport default function MBTIStep({ data, onDataChange, onNext }: MBTIStepProps) {\n  const [formData, setFormData] = useState({\n    mbtiType: data.mbtiType || '',\n    mbtiSource: data.mbtiSource || '',\n    quickTestAnswers: data.quickTestAnswers || {}\n  })\n\n  const [showQuickTest, setShowQuickTest] = useState(false)\n  const [currentQuestion, setCurrentQuestion] = useState(0)\n\n  useEffect(() => {\n    onDataChange(formData)\n  }, [formData, onDataChange])\n\n  const quickTestQuestions = [\n    {\n      question: \"在聚会中，你更倾向于：\",\n      options: [\n        { text: \"与少数几个人深入交谈\", type: \"I\" },\n        { text: \"与很多人轻松聊天\", type: \"E\" }\n      ]\n    },\n    {\n      question: \"做决定时，你更依赖：\",\n      options: [\n        { text: \"逻辑分析和客观事实\", type: \"T\" },\n        { text: \"个人价值观和他人感受\", type: \"F\" }\n      ]\n    },\n    {\n      question: \"你更喜欢：\",\n      options: [\n        { text: \"制定详细计划并按计划执行\", type: \"J\" },\n        { text: \"保持灵活性，随机应变\", type: \"P\" }\n      ]\n    },\n    {\n      question: \"学习新知识时，你更关注：\",\n      options: [\n        { text: \"具体的事实和细节\", type: \"S\" },\n        { text: \"整体概念和可能性\", type: \"N\" }\n      ]\n    }\n  ]\n\n  const handleMBTISelect = (mbtiCode: string) => {\n    setFormData(prev => ({ \n      ...prev, \n      mbtiType: mbtiCode,\n      mbtiSource: 'direct'\n    }))\n  }\n\n  const handleQuickTestAnswer = (answer: string, type: string) => {\n    const newAnswers = {\n      ...formData.quickTestAnswers,\n      [currentQuestion]: { answer, type }\n    }\n    \n    setFormData(prev => ({\n      ...prev,\n      quickTestAnswers: newAnswers\n    }))\n\n    if (currentQuestion < quickTestQuestions.length - 1) {\n      setCurrentQuestion(currentQuestion + 1)\n    } else {\n      // 计算MBTI结果\n      const types = { E: 0, I: 0, S: 0, N: 0, T: 0, F: 0, J: 0, P: 0 }\n      Object.values(newAnswers).forEach((ans: any) => {\n        types[ans.type as keyof typeof types]++\n      })\n      \n      const result = \n        (types.E > types.I ? 'E' : 'I') +\n        (types.S > types.N ? 'S' : 'N') +\n        (types.T > types.F ? 'T' : 'F') +\n        (types.J > types.P ? 'J' : 'P')\n      \n      setFormData(prev => ({\n        ...prev,\n        mbtiType: result,\n        mbtiSource: 'quickTest'\n      }))\n      setShowQuickTest(false)\n      setCurrentQuestion(0)\n    }\n  }\n\n  const startQuickTest = () => {\n    setShowQuickTest(true)\n    setCurrentQuestion(0)\n    setFormData(prev => ({ ...prev, quickTestAnswers: {} }))\n  }\n\n  const selectedMBTI = MBTI_TYPES.find(type => type.code === formData.mbtiType)\n\n  if (showQuickTest) {\n    const question = quickTestQuestions[currentQuestion]\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"text-center mb-8\">\n          <h2 className=\"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2\">\n            快速MBTI测试\n          </h2>\n          <p className=\"text-[var(--soft-gray)]\">\n            问题 {currentQuestion + 1} / {quickTestQuestions.length}\n          </p>\n        </div>\n\n        <div className=\"max-w-2xl mx-auto\">\n          <div className=\"bg-[var(--mist-white)] p-6 rounded-lg mb-6\">\n            <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n              {question.question}\n            </h3>\n            <div className=\"space-y-3\">\n              {question.options.map((option, index) => (\n                <button\n                  key={index}\n                  onClick={() => handleQuickTestAnswer(option.text, option.type)}\n                  className=\"w-full p-4 text-left bg-white rounded-lg border border-[var(--soft-gray)] hover:border-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white transition-all\"\n                >\n                  {option.text}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2\">\n          MBTI性格类型\n        </h2>\n        <p className=\"text-[var(--soft-gray)]\">\n          选择获取MBTI结果的方式\n        </p>\n      </div>\n\n      {/* 选择方式 */}\n      <div className=\"grid md:grid-cols-3 gap-6\">\n        <div className=\"bg-[var(--mist-white)] p-6 rounded-lg text-center\">\n          <Brain className=\"w-12 h-12 text-[var(--coral-pink)] mx-auto mb-4\" />\n          <h3 className=\"font-semibold text-[var(--dark-blue-gray)] mb-2\">\n            我已知道结果\n          </h3>\n          <p className=\"text-sm text-[var(--soft-gray)] mb-4\">\n            直接选择你的MBTI类型\n          </p>\n          <Button \n            variant=\"outline\" \n            size=\"sm\"\n            onClick={() => setFormData(prev => ({ ...prev, mbtiSource: 'direct' }))}\n          >\n            直接选择\n          </Button>\n        </div>\n\n        <div className=\"bg-[var(--mist-white)] p-6 rounded-lg text-center\">\n          <ExternalLink className=\"w-12 h-12 text-[var(--mint-green)] mx-auto mb-4\" />\n          <h3 className=\"font-semibold text-[var(--dark-blue-gray)] mb-2\">\n            专业测试\n          </h3>\n          <p className=\"text-sm text-[var(--soft-gray)] mb-4\">\n            跳转到专业MBTI测试网站\n          </p>\n          <Button \n            variant=\"outline\" \n            size=\"sm\"\n            onClick={() => window.open('https://www.16personalities.com/ch', '_blank')}\n          >\n            去测试\n          </Button>\n        </div>\n\n        <div className=\"bg-[var(--mist-white)] p-6 rounded-lg text-center\">\n          <CheckCircle className=\"w-12 h-12 text-[var(--brand-purple)] mx-auto mb-4\" />\n          <h3 className=\"font-semibold text-[var(--dark-blue-gray)] mb-2\">\n            快速测试\n          </h3>\n          <p className=\"text-sm text-[var(--soft-gray)] mb-4\">\n            4道题快速了解你的类型\n          </p>\n          <Button \n            variant=\"outline\" \n            size=\"sm\"\n            onClick={startQuickTest}\n          >\n            开始测试\n          </Button>\n        </div>\n      </div>\n\n      {/* MBTI类型选择 */}\n      {formData.mbtiSource === 'direct' && (\n        <div>\n          <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n            选择你的MBTI类型\n          </h3>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n            {MBTI_TYPES.map((type) => (\n              <button\n                key={type.code}\n                onClick={() => handleMBTISelect(type.code)}\n                className={`p-4 rounded-lg border text-left transition-all ${\n                  formData.mbtiType === type.code\n                    ? 'border-[var(--coral-pink)] bg-[var(--coral-pink)] text-white'\n                    : 'border-[var(--soft-gray)] hover:border-[var(--coral-pink)]'\n                }`}\n              >\n                <div className=\"font-bold text-lg\">{type.code}</div>\n                <div className=\"text-sm opacity-90\">{type.name}</div>\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 显示选中的MBTI结果 */}\n      {selectedMBTI && (\n        <div className=\"bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] p-6 rounded-lg text-white\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"text-3xl font-bold mr-4\">{selectedMBTI.code}</div>\n            <div>\n              <h3 className=\"text-xl font-semibold\">{selectedMBTI.name}</h3>\n              <p className=\"opacity-90\">{selectedMBTI.description}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"flex justify-end mt-8\">\n        <Button \n          onClick={onNext} \n          size=\"lg\"\n          disabled={!formData.mbtiType}\n        >\n          下一步：合作偏好\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AALA;;;;;AAce,SAAS,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAiB;;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU,KAAK,QAAQ,IAAI;QAC3B,YAAY,KAAK,UAAU,IAAI;QAC/B,kBAAkB,KAAK,gBAAgB,IAAI,CAAC;IAC9C;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,aAAa;QACf;6BAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,qBAAqB;QACzB;YACE,UAAU;YACV,SAAS;gBACP;oBAAE,MAAM;oBAAc,MAAM;gBAAI;gBAChC;oBAAE,MAAM;oBAAY,MAAM;gBAAI;aAC/B;QACH;QACA;YACE,UAAU;YACV,SAAS;gBACP;oBAAE,MAAM;oBAAa,MAAM;gBAAI;gBAC/B;oBAAE,MAAM;oBAAc,MAAM;gBAAI;aACjC;QACH;QACA;YACE,UAAU;YACV,SAAS;gBACP;oBAAE,MAAM;oBAAgB,MAAM;gBAAI;gBAClC;oBAAE,MAAM;oBAAc,MAAM;gBAAI;aACjC;QACH;QACA;YACE,UAAU;YACV,SAAS;gBACP;oBAAE,MAAM;oBAAY,MAAM;gBAAI;gBAC9B;oBAAE,MAAM;oBAAY,MAAM;gBAAI;aAC/B;QACH;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,UAAU;gBACV,YAAY;YACd,CAAC;IACH;IAEA,MAAM,wBAAwB,CAAC,QAAgB;QAC7C,MAAM,aAAa;YACjB,GAAG,SAAS,gBAAgB;YAC5B,CAAC,gBAAgB,EAAE;gBAAE;gBAAQ;YAAK;QACpC;QAEA,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,kBAAkB;YACpB,CAAC;QAED,IAAI,kBAAkB,mBAAmB,MAAM,GAAG,GAAG;YACnD,mBAAmB,kBAAkB;QACvC,OAAO;YACL,WAAW;YACX,MAAM,QAAQ;gBAAE,GAAG;gBAAG,GAAG;gBAAG,GAAG;gBAAG,GAAG;gBAAG,GAAG;gBAAG,GAAG;gBAAG,GAAG;gBAAG,GAAG;YAAE;YAC/D,OAAO,MAAM,CAAC,YAAY,OAAO,CAAC,CAAC;gBACjC,KAAK,CAAC,IAAI,IAAI,CAAuB;YACvC;YAEA,MAAM,SACJ,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,GAAG,IAC9B,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,GAAG,IAC9B,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,GAAG,IAC9B,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,GAAG;YAEhC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU;oBACV,YAAY;gBACd,CAAC;YACD,iBAAiB;YACjB,mBAAmB;QACrB;IACF;IAEA,MAAM,iBAAiB;QACrB,iBAAiB;QACjB,mBAAmB;QACnB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,kBAAkB,CAAC;YAAE,CAAC;IACxD;IAEA,MAAM,eAAe,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,SAAS,QAAQ;IAE5E,IAAI,eAAe;QACjB,MAAM,WAAW,kBAAkB,CAAC,gBAAgB;QACpD,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAGrE,6LAAC;4BAAE,WAAU;;gCAA0B;gCACjC,kBAAkB;gCAAE;gCAAI,mBAAmB,MAAM;;;;;;;;;;;;;8BAIzD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,SAAS,QAAQ;;;;;;0CAEpB,6LAAC;gCAAI,WAAU;0CACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC;wCAEC,SAAS,IAAM,sBAAsB,OAAO,IAAI,EAAE,OAAO,IAAI;wCAC7D,WAAU;kDAET,OAAO,IAAI;uCAJP;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAMzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAGhE,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,YAAY;wCAAS,CAAC;0CACtE;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAGhE,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC,sCAAsC;0CAClE;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAGhE,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;0CACV;;;;;;;;;;;;;;;;;;YAOJ,SAAS,UAAU,KAAK,0BACvB,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;kCACZ,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,qBACf,6LAAC;gCAEC,SAAS,IAAM,iBAAiB,KAAK,IAAI;gCACzC,WAAW,CAAC,+CAA+C,EACzD,SAAS,QAAQ,KAAK,KAAK,IAAI,GAC3B,iEACA,8DACJ;;kDAEF,6LAAC;wCAAI,WAAU;kDAAqB,KAAK,IAAI;;;;;;kDAC7C,6LAAC;wCAAI,WAAU;kDAAsB,KAAK,IAAI;;;;;;;+BATzC,KAAK,IAAI;;;;;;;;;;;;;;;;YAiBvB,8BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAA2B,aAAa,IAAI;;;;;;sCAC3D,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyB,aAAa,IAAI;;;;;;8CACxD,6LAAC;oCAAE,WAAU;8CAAc,aAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;0BAM3D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,MAAK;oBACL,UAAU,CAAC,SAAS,QAAQ;8BAC7B;;;;;;;;;;;;;;;;;AAMT;GAtPwB;KAAA", "debugId": null}}, {"offset": {"line": 1764, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/components/experience/PreferencesStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { PRESET_TAGS } from '@/data/mock-data'\n\ninterface PreferencesStepProps {\n  data: any\n  onDataChange: (data: any) => void\n  onNext: () => void\n  onPrev: () => void\n}\n\nexport default function PreferencesStep({ data, onDataChange, onNext }: PreferencesStepProps) {\n  const [formData, setFormData] = useState({\n    workStyle: data.workStyle || [],\n    teamSize: data.teamSize || '',\n    communicationStyle: data.communicationStyle || '',\n    projectType: data.projectType || [],\n    avoidTypes: data.avoidTypes || [],\n    idealPartner: data.idealPartner || ''\n  })\n\n  useEffect(() => {\n    onDataChange(formData)\n  }, [formData, onDataChange])\n\n  const handleWorkStyleToggle = (style: string) => {\n    setFormData(prev => ({\n      ...prev,\n      workStyle: prev.workStyle.includes(style)\n        ? prev.workStyle.filter((s: string) => s !== style)\n        : [...prev.workStyle, style]\n    }))\n  }\n\n  const handleProjectTypeToggle = (type: string) => {\n    setFormData(prev => ({\n      ...prev,\n      projectType: prev.projectType.includes(type)\n        ? prev.projectType.filter((t: string) => t !== type)\n        : [...prev.projectType, type]\n    }))\n  }\n\n  const handleAvoidTypeToggle = (type: string) => {\n    setFormData(prev => ({\n      ...prev,\n      avoidTypes: prev.avoidTypes.includes(type)\n        ? prev.avoidTypes.filter((t: string) => t !== type)\n        : [...prev.avoidTypes, type]\n    }))\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2\">\n          合作偏好\n        </h2>\n        <p className=\"text-[var(--soft-gray)]\">\n          告诉我们你喜欢的合作方式和项目类型\n        </p>\n      </div>\n\n      {/* 工作方式偏好 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          工作方式偏好\n        </h3>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n          {PRESET_TAGS.workStyle.map((style) => (\n            <button\n              key={style}\n              onClick={() => handleWorkStyleToggle(style)}\n              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all ${\n                formData.workStyle.includes(style)\n                  ? 'bg-[var(--mint-green)] text-white'\n                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--mint-green)] hover:text-white'\n              }`}\n            >\n              {style}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* 团队规模偏好 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          理想团队规模\n        </h3>\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n          {['2-3人小团队', '4-6人中团队', '7-10人大团队', '10人以上'].map((size) => (\n            <button\n              key={size}\n              onClick={() => setFormData(prev => ({ ...prev, teamSize: size }))}\n              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all ${\n                formData.teamSize === size\n                  ? 'bg-[var(--coral-pink)] text-white'\n                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white'\n              }`}\n            >\n              {size}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* 沟通方式 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          偏好的沟通方式\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n          {[\n            '面对面深度讨论',\n            '在线视频会议',\n            '文字消息沟通',\n            '定期汇报总结',\n            '随时随地交流',\n            '结构化会议'\n          ].map((style) => (\n            <button\n              key={style}\n              onClick={() => setFormData(prev => ({ ...prev, communicationStyle: style }))}\n              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all text-left ${\n                formData.communicationStyle === style\n                  ? 'bg-[var(--brand-purple)] text-white'\n                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--brand-purple)] hover:text-white'\n              }`}\n            >\n              {style}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* 感兴趣的项目类型 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          感兴趣的项目类型\n        </h3>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n          {PRESET_TAGS.interests.map((interest) => (\n            <button\n              key={interest}\n              onClick={() => handleProjectTypeToggle(interest)}\n              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all ${\n                formData.projectType.includes(interest)\n                  ? 'bg-[var(--ice-blue)] text-white'\n                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--ice-blue)] hover:text-white'\n              }`}\n            >\n              {interest}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* 不喜欢的合作类型 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          不太适合的合作类型（可选）\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n          {[\n            '高压力快节奏',\n            '需要频繁出差',\n            '长期加班项目',\n            '高风险投资',\n            '纯技术导向',\n            '纯商业导向'\n          ].map((type) => (\n            <button\n              key={type}\n              onClick={() => handleAvoidTypeToggle(type)}\n              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all text-left ${\n                formData.avoidTypes.includes(type)\n                  ? 'bg-red-500 text-white'\n                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-red-500 hover:text-white'\n              }`}\n            >\n              {type}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* 理想合作伙伴描述 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          理想合作伙伴描述（可选）\n        </h3>\n        <textarea\n          value={formData.idealPartner}\n          onChange={(e) => setFormData(prev => ({ ...prev, idealPartner: e.target.value }))}\n          rows={4}\n          className=\"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent\"\n          placeholder=\"描述你理想中的合作伙伴特质、工作风格等...\"\n        />\n      </div>\n\n      <div className=\"flex justify-end mt-8\">\n        <Button onClick={onNext} size=\"lg\">\n          下一步：时间与动机\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAae,SAAS,gBAAgB,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAwB;;IAC1F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW,KAAK,SAAS,IAAI,EAAE;QAC/B,UAAU,KAAK,QAAQ,IAAI;QAC3B,oBAAoB,KAAK,kBAAkB,IAAI;QAC/C,aAAa,KAAK,WAAW,IAAI,EAAE;QACnC,YAAY,KAAK,UAAU,IAAI,EAAE;QACjC,cAAc,KAAK,YAAY,IAAI;IACrC;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,aAAa;QACf;oCAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,wBAAwB,CAAC;QAC7B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW,KAAK,SAAS,CAAC,QAAQ,CAAC,SAC/B,KAAK,SAAS,CAAC,MAAM,CAAC,CAAC,IAAc,MAAM,SAC3C;uBAAI,KAAK,SAAS;oBAAE;iBAAM;YAChC,CAAC;IACH;IAEA,MAAM,0BAA0B,CAAC;QAC/B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,CAAC,QAAQ,CAAC,QACnC,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,IAAc,MAAM,QAC7C;uBAAI,KAAK,WAAW;oBAAE;iBAAK;YACjC,CAAC;IACH;IAEA,MAAM,wBAAwB,CAAC;QAC7B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,CAAC,QAAQ,CAAC,QACjC,KAAK,UAAU,CAAC,MAAM,CAAC,CAAC,IAAc,MAAM,QAC5C;uBAAI,KAAK,UAAU;oBAAE;iBAAK;YAChC,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAMzC,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;kCACZ,8HAAA,CAAA,cAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,sBAC1B,6LAAC;gCAEC,SAAS,IAAM,sBAAsB;gCACrC,WAAW,CAAC,wDAAwD,EAClE,SAAS,SAAS,CAAC,QAAQ,CAAC,SACxB,sCACA,qGACJ;0CAED;+BARI;;;;;;;;;;;;;;;;0BAeb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAW;4BAAW;4BAAY;yBAAQ,CAAC,GAAG,CAAC,CAAC,qBAChD,6LAAC;gCAEC,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU;wCAAK,CAAC;gCAC/D,WAAW,CAAC,wDAAwD,EAClE,SAAS,QAAQ,KAAK,OAClB,sCACA,qGACJ;0CAED;+BARI;;;;;;;;;;;;;;;;0BAeb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;kCACZ;4BACC;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD,CAAC,GAAG,CAAC,CAAC,sBACL,6LAAC;gCAEC,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,oBAAoB;wCAAM,CAAC;gCAC1E,WAAW,CAAC,kEAAkE,EAC5E,SAAS,kBAAkB,KAAK,QAC5B,wCACA,uGACJ;0CAED;+BARI;;;;;;;;;;;;;;;;0BAeb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;kCACZ,8HAAA,CAAA,cAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,yBAC1B,6LAAC;gCAEC,SAAS,IAAM,wBAAwB;gCACvC,WAAW,CAAC,wDAAwD,EAClE,SAAS,WAAW,CAAC,QAAQ,CAAC,YAC1B,oCACA,mGACJ;0CAED;+BARI;;;;;;;;;;;;;;;;0BAeb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;kCACZ;4BACC;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;gCAEC,SAAS,IAAM,sBAAsB;gCACrC,WAAW,CAAC,kEAAkE,EAC5E,SAAS,UAAU,CAAC,QAAQ,CAAC,QACzB,0BACA,yFACJ;0CAED;+BARI;;;;;;;;;;;;;;;;0BAeb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBACC,OAAO,SAAS,YAAY;wBAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gCAAC,CAAC;wBAC/E,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAQ,MAAK;8BAAK;;;;;;;;;;;;;;;;;AAM3C;GArMwB;KAAA", "debugId": null}}, {"offset": {"line": 2101, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/components/experience/TimeMotivationStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Clock, Target, Heart } from 'lucide-react'\n\ninterface TimeMotivationStepProps {\n  data: any\n  onDataChange: (data: any) => void\n  onNext: () => void\n  onPrev: () => void\n}\n\nexport default function TimeMotivationStep({ data, onDataChange, onNext }: TimeMotivationStepProps) {\n  const [formData, setFormData] = useState({\n    timeAvailable: data.timeAvailable || '',\n    timePreference: data.timePreference || '',\n    commitment: data.commitment || '',\n    motivation: data.motivation || [],\n    goals: data.goals || '',\n    timeline: data.timeline || ''\n  })\n\n  useEffect(() => {\n    onDataChange(formData)\n  }, [formData, onDataChange])\n\n  const handleMotivationToggle = (motivation: string) => {\n    setFormData(prev => ({\n      ...prev,\n      motivation: prev.motivation.includes(motivation)\n        ? prev.motivation.filter((m: string) => m !== motivation)\n        : [...prev.motivation, motivation]\n    }))\n  }\n\n  const motivations = [\n    '学习新技能',\n    '获得收入',\n    '积累经验',\n    '扩展人脉',\n    '实现创意',\n    '解决问题',\n    '帮助他人',\n    '个人成长',\n    '行业影响力',\n    '创业梦想'\n  ]\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2\">\n          时间投入与动机\n        </h2>\n        <p className=\"text-[var(--soft-gray)]\">\n          了解你的时间安排和参与动机\n        </p>\n      </div>\n\n      {/* 可投入时间 */}\n      <div className=\"grid md:grid-cols-2 gap-6\">\n        <div>\n          <div className=\"flex items-center mb-4\">\n            <Clock className=\"w-5 h-5 text-[var(--coral-pink)] mr-2\" />\n            <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)]\">\n              每周可投入时间\n            </h3>\n          </div>\n          <div className=\"space-y-3\">\n            {[\n              '5小时以下（业余时间）',\n              '5-15小时（兼职投入）',\n              '15-30小时（半职投入）',\n              '30小时以上（全职投入）'\n            ].map((time) => (\n              <button\n                key={time}\n                onClick={() => setFormData(prev => ({ ...prev, timeAvailable: time }))}\n                className={`w-full px-4 py-3 rounded-lg text-left transition-all ${\n                  formData.timeAvailable === time\n                    ? 'bg-[var(--coral-pink)] text-white'\n                    : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white'\n                }`}\n              >\n                {time}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div>\n          <div className=\"flex items-center mb-4\">\n            <Target className=\"w-5 h-5 text-[var(--mint-green)] mr-2\" />\n            <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)]\">\n              时间偏好\n            </h3>\n          </div>\n          <div className=\"space-y-3\">\n            {[\n              '工作日晚上',\n              '周末集中时间',\n              '灵活安排',\n              '固定时间段'\n            ].map((pref) => (\n              <button\n                key={pref}\n                onClick={() => setFormData(prev => ({ ...prev, timePreference: pref }))}\n                className={`w-full px-4 py-3 rounded-lg text-left transition-all ${\n                  formData.timePreference === pref\n                    ? 'bg-[var(--mint-green)] text-white'\n                    : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--mint-green)] hover:text-white'\n                }`}\n              >\n                {pref}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* 承诺程度 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          项目承诺程度\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {[\n            { \n              level: '轻度参与', \n              desc: '尝试性参与，可随时退出',\n              color: 'var(--ice-blue)'\n            },\n            { \n              level: '中度承诺', \n              desc: '认真参与，但保持灵活性',\n              color: 'var(--mint-green)'\n            },\n            { \n              level: '高度承诺', \n              desc: '全力投入，长期合作',\n              color: 'var(--coral-pink)'\n            }\n          ].map((item) => (\n            <button\n              key={item.level}\n              onClick={() => setFormData(prev => ({ ...prev, commitment: item.level }))}\n              className={`p-4 rounded-lg border-2 text-left transition-all ${\n                formData.commitment === item.level\n                  ? `border-[${item.color}] bg-[${item.color}] text-white`\n                  : 'border-[var(--soft-gray)] hover:border-[var(--coral-pink)]'\n              }`}\n            >\n              <div className=\"font-semibold mb-2\">{item.level}</div>\n              <div className=\"text-sm opacity-90\">{item.desc}</div>\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* 参与动机 */}\n      <div>\n        <div className=\"flex items-center mb-4\">\n          <Heart className=\"w-5 h-5 text-[var(--brand-purple)] mr-2\" />\n          <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)]\">\n            参与动机（可多选）\n          </h3>\n        </div>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3\">\n          {motivations.map((motivation) => (\n            <button\n              key={motivation}\n              onClick={() => handleMotivationToggle(motivation)}\n              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${\n                formData.motivation.includes(motivation)\n                  ? 'bg-[var(--brand-purple)] text-white'\n                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--brand-purple)] hover:text-white'\n              }`}\n            >\n              {motivation}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* 目标描述 */}\n      <div className=\"grid md:grid-cols-2 gap-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n            个人目标\n          </label>\n          <textarea\n            value={formData.goals}\n            onChange={(e) => setFormData(prev => ({ ...prev, goals: e.target.value }))}\n            rows={4}\n            className=\"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent\"\n            placeholder=\"描述你希望通过合作实现的个人目标...\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2\">\n            期望时间线\n          </label>\n          <select\n            value={formData.timeline}\n            onChange={(e) => setFormData(prev => ({ ...prev, timeline: e.target.value }))}\n            className=\"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent mb-4\"\n          >\n            <option value=\"\">选择项目时间线</option>\n            <option value=\"1-3months\">1-3个月短期项目</option>\n            <option value=\"3-6months\">3-6个月中期项目</option>\n            <option value=\"6-12months\">6-12个月长期项目</option>\n            <option value=\"1year+\">1年以上持续项目</option>\n          </select>\n          \n          <div className=\"text-sm text-[var(--soft-gray)] bg-[var(--mist-white)] p-3 rounded-lg\">\n            💡 <strong>提示：</strong>明确的时间预期有助于找到合适的合作伙伴\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex justify-end mt-8\">\n        <Button onClick={onNext} size=\"lg\">\n          下一步：标签画像\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAae,SAAS,mBAAmB,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAA2B;;IAChG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,eAAe,KAAK,aAAa,IAAI;QACrC,gBAAgB,KAAK,cAAc,IAAI;QACvC,YAAY,KAAK,UAAU,IAAI;QAC/B,YAAY,KAAK,UAAU,IAAI,EAAE;QACjC,OAAO,KAAK,KAAK,IAAI;QACrB,UAAU,KAAK,QAAQ,IAAI;IAC7B;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,aAAa;QACf;uCAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,yBAAyB,CAAC;QAC9B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,CAAC,QAAQ,CAAC,cACjC,KAAK,UAAU,CAAC,MAAM,CAAC,CAAC,IAAc,MAAM,cAC5C;uBAAI,KAAK,UAAU;oBAAE;iBAAW;YACtC,CAAC;IACH;IAEA,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAMzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;;;;;;;0CAIrE,6LAAC;gCAAI,WAAU;0CACZ;oCACC;oCACA;oCACA;oCACA;iCACD,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;wCAEC,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,eAAe;gDAAK,CAAC;wCACpE,WAAW,CAAC,qDAAqD,EAC/D,SAAS,aAAa,KAAK,OACvB,sCACA,qGACJ;kDAED;uCARI;;;;;;;;;;;;;;;;kCAcb,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;;;;;;;0CAIrE,6LAAC;gCAAI,WAAU;0CACZ;oCACC;oCACA;oCACA;oCACA;iCACD,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;wCAEC,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,gBAAgB;gDAAK,CAAC;wCACrE,WAAW,CAAC,qDAAqD,EAC/D,SAAS,cAAc,KAAK,OACxB,sCACA,qGACJ;kDAED;uCARI;;;;;;;;;;;;;;;;;;;;;;0BAgBf,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;kCACZ;4BACC;gCACE,OAAO;gCACP,MAAM;gCACN,OAAO;4BACT;4BACA;gCACE,OAAO;gCACP,MAAM;gCACN,OAAO;4BACT;4BACA;gCACE,OAAO;gCACP,MAAM;gCACN,OAAO;4BACT;yBACD,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;gCAEC,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,YAAY,KAAK,KAAK;wCAAC,CAAC;gCACvE,WAAW,CAAC,iDAAiD,EAC3D,SAAS,UAAU,KAAK,KAAK,KAAK,GAC9B,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,MAAM,EAAE,KAAK,KAAK,CAAC,YAAY,CAAC,GACtD,8DACJ;;kDAEF,6LAAC;wCAAI,WAAU;kDAAsB,KAAK,KAAK;;;;;;kDAC/C,6LAAC;wCAAI,WAAU;kDAAsB,KAAK,IAAI;;;;;;;+BATzC,KAAK,KAAK;;;;;;;;;;;;;;;;0BAgBvB,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAG,WAAU;0CAAqD;;;;;;;;;;;;kCAIrE,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC;gCAEC,SAAS,IAAM,uBAAuB;gCACtC,WAAW,CAAC,wDAAwD,EAClE,SAAS,UAAU,CAAC,QAAQ,CAAC,cACzB,wCACA,uGACJ;0CAED;+BARI;;;;;;;;;;;;;;;;0BAeb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA8D;;;;;;0CAG/E,6LAAC;gCACC,OAAO,SAAS,KAAK;gCACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACxE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA8D;;;;;;0CAG/E,6LAAC;gCACC,OAAO,SAAS,QAAQ;gCACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCAC3E,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,6LAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;0CAGzB,6LAAC;gCAAI,WAAU;;oCAAwE;kDAClF,6LAAC;kDAAO;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;0BAK7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAQ,MAAK;8BAAK;;;;;;;;;;;;;;;;;AAM3C;GAxNwB;KAAA", "debugId": null}}, {"offset": {"line": 2583, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/components/experience/TagsStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { PRESET_TAGS } from '@/data/mock-data'\nimport { Plus, X } from 'lucide-react'\n\ninterface TagsStepProps {\n  data: any\n  onDataChange: (data: any) => void\n  onNext: () => void\n  onPrev: () => void\n}\n\nexport default function TagsStep({ data, onDataChange, onNext }: TagsStepProps) {\n  const [formData, setFormData] = useState({\n    personalityTags: data.personalityTags || [],\n    customTags: data.customTags || [],\n    newTag: ''\n  })\n\n  useEffect(() => {\n    onDataChange(formData)\n  }, [formData, onDataChange])\n\n  const handleTagToggle = (tag: string) => {\n    setFormData(prev => ({\n      ...prev,\n      personalityTags: prev.personalityTags.includes(tag)\n        ? prev.personalityTags.filter((t: string) => t !== tag)\n        : [...prev.personalityTags, tag]\n    }))\n  }\n\n  const handleAddCustomTag = () => {\n    if (formData.newTag.trim() && !formData.customTags.includes(formData.newTag.trim())) {\n      setFormData(prev => ({\n        ...prev,\n        customTags: [...prev.customTags, prev.newTag.trim()],\n        newTag: ''\n      }))\n    }\n  }\n\n  const removeCustomTag = (tag: string) => {\n    setFormData(prev => ({\n      ...prev,\n      customTags: prev.customTags.filter((t: string) => t !== tag)\n    }))\n  }\n\n  const allSelectedTags = [...formData.personalityTags, ...formData.customTags]\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2\">\n          个性标签画像\n        </h2>\n        <p className=\"text-[var(--soft-gray)]\">\n          选择最能代表你的标签，构建你的个性画像\n        </p>\n      </div>\n\n      {/* 性格特质标签 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          性格特质\n        </h3>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\">\n          {PRESET_TAGS.personality.map((tag) => (\n            <button\n              key={tag}\n              onClick={() => handleTagToggle(tag)}\n              className={`px-4 py-3 rounded-lg text-sm font-medium transition-all ${\n                formData.personalityTags.includes(tag)\n                  ? 'bg-[var(--coral-pink)] text-white'\n                  : 'bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white'\n              }`}\n            >\n              {tag}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* 自定义标签 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          自定义标签\n        </h3>\n        <div className=\"flex gap-2 mb-4\">\n          <input\n            type=\"text\"\n            value={formData.newTag}\n            onChange={(e) => setFormData(prev => ({ ...prev, newTag: e.target.value }))}\n            placeholder=\"添加个性化标签...\"\n            className=\"flex-1 px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent\"\n            onKeyPress={(e) => e.key === 'Enter' && handleAddCustomTag()}\n          />\n          <Button onClick={handleAddCustomTag} variant=\"outline\">\n            <Plus className=\"w-4 h-4\" />\n          </Button>\n        </div>\n\n        {formData.customTags.length > 0 && (\n          <div className=\"flex flex-wrap gap-2\">\n            {formData.customTags.map((tag: string) => (\n              <span\n                key={tag}\n                className=\"inline-flex items-center px-3 py-1 bg-[var(--mint-green)] text-white text-sm rounded-full\"\n              >\n                {tag}\n                <button\n                  onClick={() => removeCustomTag(tag)}\n                  className=\"ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-0.5\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </span>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* 标签预览 */}\n      {allSelectedTags.length > 0 && (\n        <div className=\"bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] p-6 rounded-lg\">\n          <h3 className=\"text-lg font-semibold text-white mb-4\">\n            你的个性画像预览\n          </h3>\n          <div className=\"flex flex-wrap gap-2\">\n            {allSelectedTags.map((tag: string, index: number) => (\n              <span\n                key={index}\n                className=\"px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-full\"\n              >\n                {tag}\n              </span>\n            ))}\n          </div>\n          <p className=\"text-white text-sm mt-4 opacity-90\">\n            已选择 {allSelectedTags.length} 个标签\n          </p>\n        </div>\n      )}\n\n      {/* 标签建议 */}\n      <div className=\"bg-[var(--mist-white)] p-6 rounded-lg\">\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-3\">\n          💡 标签选择建议\n        </h3>\n        <div className=\"grid md:grid-cols-2 gap-4 text-sm text-[var(--soft-gray)]\">\n          <div>\n            <strong className=\"text-[var(--dark-blue-gray)]\">选择原则：</strong>\n            <ul className=\"mt-2 space-y-1\">\n              <li>• 选择5-8个最能代表你的标签</li>\n              <li>• 包含工作风格和性格特质</li>\n              <li>• 避免选择过多相似标签</li>\n            </ul>\n          </div>\n          <div>\n            <strong className=\"text-[var(--dark-blue-gray)]\">标签作用：</strong>\n            <ul className=\"mt-2 space-y-1\">\n              <li>• 帮助他人快速了解你</li>\n              <li>• 提高匹配准确度</li>\n              <li>• 展示你的独特性</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex justify-end mt-8\">\n        <Button \n          onClick={onNext} \n          size=\"lg\"\n          disabled={allSelectedTags.length === 0}\n        >\n          下一步：能力评估\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AAce,SAAS,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAiB;;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,iBAAiB,KAAK,eAAe,IAAI,EAAE;QAC3C,YAAY,KAAK,UAAU,IAAI,EAAE;QACjC,QAAQ;IACV;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,aAAa;QACf;6BAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,kBAAkB,CAAC;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,iBAAiB,KAAK,eAAe,CAAC,QAAQ,CAAC,OAC3C,KAAK,eAAe,CAAC,MAAM,CAAC,CAAC,IAAc,MAAM,OACjD;uBAAI,KAAK,eAAe;oBAAE;iBAAI;YACpC,CAAC;IACH;IAEA,MAAM,qBAAqB;QACzB,IAAI,SAAS,MAAM,CAAC,IAAI,MAAM,CAAC,SAAS,UAAU,CAAC,QAAQ,CAAC,SAAS,MAAM,CAAC,IAAI,KAAK;YACnF,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,YAAY;2BAAI,KAAK,UAAU;wBAAE,KAAK,MAAM,CAAC,IAAI;qBAAG;oBACpD,QAAQ;gBACV,CAAC;QACH;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY,KAAK,UAAU,CAAC,MAAM,CAAC,CAAC,IAAc,MAAM;YAC1D,CAAC;IACH;IAEA,MAAM,kBAAkB;WAAI,SAAS,eAAe;WAAK,SAAS,UAAU;KAAC;IAE7E,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAMzC,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;kCACZ,8HAAA,CAAA,cAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,oBAC5B,6LAAC;gCAEC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,wDAAwD,EAClE,SAAS,eAAe,CAAC,QAAQ,CAAC,OAC9B,sCACA,qGACJ;0CAED;+BARI;;;;;;;;;;;;;;;;0BAeb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,OAAO,SAAS,MAAM;gCACtB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wCAAC,CAAC;gCACzE,aAAY;gCACZ,WAAU;gCACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;0CAE1C,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAoB,SAAQ;0CAC3C,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAInB,SAAS,UAAU,CAAC,MAAM,GAAG,mBAC5B,6LAAC;wBAAI,WAAU;kCACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,oBACxB,6LAAC;gCAEC,WAAU;;oCAET;kDACD,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BARV;;;;;;;;;;;;;;;;YAiBd,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,KAAa,sBACjC,6LAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;;;;;;kCAOX,6LAAC;wBAAE,WAAU;;4BAAqC;4BAC3C,gBAAgB,MAAM;4BAAC;;;;;;;;;;;;;0BAMlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAO,WAAU;kDAA+B;;;;;;kDACjD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;;kDACC,6LAAC;wCAAO,WAAU;kDAA+B;;;;;;kDACjD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,MAAK;oBACL,UAAU,gBAAgB,MAAM,KAAK;8BACtC;;;;;;;;;;;;;;;;;AAMT;GAzKwB;KAAA", "debugId": null}}, {"offset": {"line": 2991, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/components/experience/RadarStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { RADAR_DIMENSIONS } from '@/data/mock-data'\nimport { Radar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, ResponsiveContainer } from 'recharts'\n\ninterface RadarStepProps {\n  data: any\n  onDataChange: (data: any) => void\n  onNext: () => void\n  onPrev: () => void\n}\n\nexport default function RadarStep({ data, onDataChange, onNext }: RadarStepProps) {\n  const [formData, setFormData] = useState({\n    radarData: data.radarData || RADAR_DIMENSIONS.reduce((acc, dim) => ({\n      ...acc,\n      [dim.key]: 5\n    }), {})\n  })\n\n  useEffect(() => {\n    onDataChange(formData)\n  }, [formData, onDataChange])\n\n  const handleScoreChange = (dimension: string, score: number) => {\n    setFormData(prev => ({\n      ...prev,\n      radarData: {\n        ...prev.radarData,\n        [dimension]: score\n      }\n    }))\n  }\n\n  const radarChartData = RADAR_DIMENSIONS.map(dim => ({\n    dimension: dim.label,\n    value: formData.radarData[dim.key] || 5,\n    fullMark: 10\n  }))\n\n  const getScoreDescription = (score: number) => {\n    if (score <= 3) return '初级'\n    if (score <= 6) return '中级'\n    if (score <= 8) return '高级'\n    return '专家'\n  }\n\n  const getScoreColor = (score: number) => {\n    if (score <= 3) return '#ff6b6b'\n    if (score <= 6) return '#ffa726'\n    if (score <= 8) return '#66bb6a'\n    return '#42a5f5'\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2\">\n          六维能力评估\n        </h2>\n        <p className=\"text-[var(--soft-gray)]\">\n          诚实评估你在各个维度的能力水平\n        </p>\n      </div>\n\n      <div className=\"grid lg:grid-cols-2 gap-8\">\n        {/* 能力评分 */}\n        <div className=\"space-y-6\">\n          {RADAR_DIMENSIONS.map((dimension) => (\n            <div key={dimension.key} className=\"bg-[var(--mist-white)] p-4 rounded-lg\">\n              <div className=\"flex justify-between items-center mb-3\">\n                <div>\n                  <h3 className=\"font-semibold text-[var(--dark-blue-gray)]\">\n                    {dimension.label}\n                  </h3>\n                  <p className=\"text-sm text-[var(--soft-gray)]\">\n                    {dimension.description}\n                  </p>\n                </div>\n                <div className=\"text-right\">\n                  <div \n                    className=\"text-2xl font-bold\"\n                    style={{ color: getScoreColor(formData.radarData[dimension.key]) }}\n                  >\n                    {formData.radarData[dimension.key]}\n                  </div>\n                  <div className=\"text-xs text-[var(--soft-gray)]\">\n                    {getScoreDescription(formData.radarData[dimension.key])}\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <input\n                  type=\"range\"\n                  min=\"1\"\n                  max=\"10\"\n                  value={formData.radarData[dimension.key]}\n                  onChange={(e) => handleScoreChange(dimension.key, parseInt(e.target.value))}\n                  className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider\"\n                  style={{\n                    background: `linear-gradient(to right, ${getScoreColor(formData.radarData[dimension.key])} 0%, ${getScoreColor(formData.radarData[dimension.key])} ${formData.radarData[dimension.key] * 10}%, #e5e7eb ${formData.radarData[dimension.key] * 10}%, #e5e7eb 100%)`\n                  }}\n                />\n                <div className=\"flex justify-between text-xs text-[var(--soft-gray)]\">\n                  <span>1 (初学)</span>\n                  <span>5 (中等)</span>\n                  <span>10 (专家)</span>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* 雷达图 */}\n        <div className=\"bg-white p-6 rounded-lg card-shadow\">\n          <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4 text-center\">\n            能力雷达图\n          </h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <RadarChart data={radarChartData}>\n                <PolarGrid />\n                <PolarAngleAxis \n                  dataKey=\"dimension\" \n                  tick={{ fontSize: 12, fill: '#7F8C8D' }}\n                />\n                <PolarRadiusAxis \n                  angle={90} \n                  domain={[0, 10]} \n                  tick={{ fontSize: 10, fill: '#BDC3C7' }}\n                />\n                <Radar\n                  name=\"能力值\"\n                  dataKey=\"value\"\n                  stroke=\"#FF6B6B\"\n                  fill=\"#FF6B6B\"\n                  fillOpacity={0.3}\n                  strokeWidth={2}\n                />\n              </RadarChart>\n            </ResponsiveContainer>\n          </div>\n          \n          {/* 总体评分 */}\n          <div className=\"mt-4 text-center\">\n            <div className=\"text-sm text-[var(--soft-gray)] mb-2\">综合能力评分</div>\n            <div className=\"text-2xl font-bold text-[var(--coral-pink)]\">\n              {(Object.values(formData.radarData).reduce((sum: number, score: any) => sum + score, 0) / 6).toFixed(1)}\n            </div>\n            <div className=\"text-xs text-[var(--soft-gray)]\">\n              / 10.0\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 评分指南 */}\n      <div className=\"bg-[var(--mist-white)] p-6 rounded-lg\">\n        <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4\">\n          📊 评分参考指南\n        </h3>\n        <div className=\"grid md:grid-cols-2 gap-6 text-sm\">\n          <div>\n            <h4 className=\"font-semibold text-[var(--dark-blue-gray)] mb-2\">评分标准：</h4>\n            <ul className=\"space-y-1 text-[var(--soft-gray)]\">\n              <li><strong>1-3分：</strong> 初学者水平，需要学习和提升</li>\n              <li><strong>4-6分：</strong> 中等水平，有一定经验</li>\n              <li><strong>7-8分：</strong> 高级水平，经验丰富</li>\n              <li><strong>9-10分：</strong> 专家水平，可以指导他人</li>\n            </ul>\n          </div>\n          <div>\n            <h4 className=\"font-semibold text-[var(--dark-blue-gray)] mb-2\">诚实评估的重要性：</h4>\n            <ul className=\"space-y-1 text-[var(--soft-gray)]\">\n              <li>• 帮助找到互补的合作伙伴</li>\n              <li>• 避免能力不匹配的问题</li>\n              <li>• 建立真实可信的合作关系</li>\n              <li>• 促进团队协作效率</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex justify-end mt-8\">\n        <Button onClick={onNext} size=\"lg\">\n          下一步：信息确认\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAce,SAAS,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAkB;;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW,KAAK,SAAS,IAAI,8HAAA,CAAA,mBAAgB,CAAC,MAAM;kCAAC,CAAC,KAAK,MAAQ,CAAC;oBAClE,GAAG,GAAG;oBACN,CAAC,IAAI,GAAG,CAAC,EAAE;gBACb,CAAC;iCAAG,CAAC;IACP;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,aAAa;QACf;8BAAG;QAAC;QAAU;KAAa;IAE3B,MAAM,oBAAoB,CAAC,WAAmB;QAC5C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW;oBACT,GAAG,KAAK,SAAS;oBACjB,CAAC,UAAU,EAAE;gBACf;YACF,CAAC;IACH;IAEA,MAAM,iBAAiB,8HAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;YAClD,WAAW,IAAI,KAAK;YACpB,OAAO,SAAS,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI;YACtC,UAAU;QACZ,CAAC;IAED,MAAM,sBAAsB,CAAC;QAC3B,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACZ,8HAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,0BACrB,6LAAC;gCAAwB,WAAU;;kDACjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,UAAU,KAAK;;;;;;kEAElB,6LAAC;wDAAE,WAAU;kEACV,UAAU,WAAW;;;;;;;;;;;;0DAG1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,cAAc,SAAS,SAAS,CAAC,UAAU,GAAG,CAAC;wDAAE;kEAEhE,SAAS,SAAS,CAAC,UAAU,GAAG,CAAC;;;;;;kEAEpC,6LAAC;wDAAI,WAAU;kEACZ,oBAAoB,SAAS,SAAS,CAAC,UAAU,GAAG,CAAC;;;;;;;;;;;;;;;;;;kDAK5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,SAAS,SAAS,CAAC,UAAU,GAAG,CAAC;gDACxC,UAAU,CAAC,IAAM,kBAAkB,UAAU,GAAG,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDACzE,WAAU;gDACV,OAAO;oDACL,YAAY,CAAC,0BAA0B,EAAE,cAAc,SAAS,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,KAAK,EAAE,cAAc,SAAS,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE,SAAS,SAAS,CAAC,UAAU,GAAG,CAAC,GAAG,GAAG,WAAW,EAAE,SAAS,SAAS,CAAC,UAAU,GAAG,CAAC,GAAG,GAAG,gBAAgB,CAAC;gDACnQ;;;;;;0DAEF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;+BAtCF,UAAU,GAAG;;;;;;;;;;kCA8C3B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsE;;;;;;0CAGpF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAO;8CACvC,cAAA,6LAAC,yJAAA,CAAA,aAAU;wCAAC,MAAM;;0DAChB,6LAAC,wJAAA,CAAA,YAAS;;;;;0DACV,6LAAC,6JAAA,CAAA,iBAAc;gDACb,SAAQ;gDACR,MAAM;oDAAE,UAAU;oDAAI,MAAM;gDAAU;;;;;;0DAExC,6LAAC,8JAAA,CAAA,kBAAe;gDACd,OAAO;gDACP,QAAQ;oDAAC;oDAAG;iDAAG;gDACf,MAAM;oDAAE,UAAU;oDAAI,MAAM;gDAAU;;;;;;0DAExC,6LAAC,oJAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,SAAQ;gDACR,QAAO;gDACP,MAAK;gDACL,aAAa;gDACb,aAAa;;;;;;;;;;;;;;;;;;;;;;0CAOrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAuC;;;;;;kDACtD,6LAAC;wCAAI,WAAU;kDACZ,CAAC,OAAO,MAAM,CAAC,SAAS,SAAS,EAAE,MAAM,CAAC,CAAC,KAAa,QAAe,MAAM,OAAO,KAAK,CAAC,EAAE,OAAO,CAAC;;;;;;kDAEvG,6LAAC;wCAAI,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;0BAQvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0D;;;;;;kCAGxE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;;kEAAG,6LAAC;kEAAO;;;;;;oDAAc;;;;;;;0DAC1B,6LAAC;;kEAAG,6LAAC;kEAAO;;;;;;oDAAc;;;;;;;0DAC1B,6LAAC;;kEAAG,6LAAC;kEAAO;;;;;;oDAAc;;;;;;;0DAC1B,6LAAC;;kEAAG,6LAAC;kEAAO;;;;;;oDAAe;;;;;;;;;;;;;;;;;;;0CAG/B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAQ,MAAK;8BAAK;;;;;;;;;;;;;;;;;AAM3C;GAnLwB;KAAA", "debugId": null}}, {"offset": {"line": 3544, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/components/experience/SummaryStep.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { MBTI_TYPES, RADAR_DIMENSIONS } from '@/data/mock-data'\nimport { User, Briefcase, Brain, Heart, Clock, Tags, BarChart3, CheckCircle } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface SummaryStepProps {\n  data: any\n  onDataChange: (data: any) => void\n  onNext: () => void\n  onPrev: () => void\n}\n\nexport default function SummaryStep({ data }: SummaryStepProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [isSubmitted, setIsSubmitted] = useState(false)\n\n  const selectedMBTI = MBTI_TYPES.find(type => type.code === data.mbtiType)\n  const allTags = [...(data.personalityTags || []), ...(data.customTags || [])]\n  const averageScore = data.radarData \n    ? (Object.values(data.radarData).reduce((sum: number, score: any) => sum + score, 0) / 6).toFixed(1)\n    : '0'\n\n  const handleSubmit = async () => {\n    setIsSubmitting(true)\n    // 模拟提交过程\n    await new Promise(resolve => setTimeout(resolve, 2000))\n    setIsSubmitting(false)\n    setIsSubmitted(true)\n  }\n\n  if (isSubmitted) {\n    return (\n      <div className=\"text-center space-y-6\">\n        <div className=\"w-20 h-20 bg-[var(--mint-green)] rounded-full flex items-center justify-center mx-auto\">\n          <CheckCircle className=\"w-10 h-10 text-white\" />\n        </div>\n        <h2 className=\"text-2xl font-bold text-[var(--dark-blue-gray)]\">\n          🎉 合作画像创建成功！\n        </h2>\n        <p className=\"text-[var(--soft-gray)] max-w-md mx-auto\">\n          你的个人合作画像已经创建完成。现在可以查看为你推荐的合作伙伴和项目了！\n        </p>\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Link href=\"/profile\">\n            <Button size=\"lg\">\n              查看我的画像\n            </Button>\n          </Link>\n          <Link href=\"/match\">\n            <Button variant=\"outline\" size=\"lg\">\n              查看匹配结果\n            </Button>\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2\">\n          信息确认\n        </h2>\n        <p className=\"text-[var(--soft-gray)]\">\n          请确认你的合作画像信息，确认无误后即可完成创建\n        </p>\n      </div>\n\n      <div className=\"grid lg:grid-cols-2 gap-8\">\n        {/* 基本信息 */}\n        <div className=\"bg-[var(--mist-white)] p-6 rounded-lg\">\n          <div className=\"flex items-center mb-4\">\n            <User className=\"w-5 h-5 text-[var(--coral-pink)] mr-2\" />\n            <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)]\">基本信息</h3>\n          </div>\n          <div className=\"space-y-2 text-sm\">\n            <div><strong>姓名：</strong>{data.name}</div>\n            <div><strong>年龄：</strong>{data.age}</div>\n            <div><strong>性别：</strong>{data.gender}</div>\n            <div><strong>地区：</strong>{data.location}</div>\n            {data.email && <div><strong>邮箱：</strong>{data.email}</div>}\n          </div>\n        </div>\n\n        {/* 技能资源 */}\n        <div className=\"bg-[var(--mist-white)] p-6 rounded-lg\">\n          <div className=\"flex items-center mb-4\">\n            <Briefcase className=\"w-5 h-5 text-[var(--mint-green)] mr-2\" />\n            <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)]\">技能资源</h3>\n          </div>\n          <div className=\"space-y-3\">\n            {data.skills && data.skills.length > 0 && (\n              <div>\n                <strong className=\"text-sm\">技能：</strong>\n                <div className=\"flex flex-wrap gap-1 mt-1\">\n                  {data.skills.map((skill: string, index: number) => (\n                    <span key={index} className=\"px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded\">\n                      {skill}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            )}\n            {data.funding && (\n              <div className=\"text-sm\">\n                <strong>资金情况：</strong>{data.funding}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* MBTI */}\n        {selectedMBTI && (\n          <div className=\"bg-[var(--mist-white)] p-6 rounded-lg\">\n            <div className=\"flex items-center mb-4\">\n              <Brain className=\"w-5 h-5 text-[var(--brand-purple)] mr-2\" />\n              <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)]\">MBTI类型</h3>\n            </div>\n            <div className=\"flex items-center\">\n              <div className=\"text-2xl font-bold text-[var(--brand-purple)] mr-3\">\n                {selectedMBTI.code}\n              </div>\n              <div>\n                <div className=\"font-semibold\">{selectedMBTI.name}</div>\n                <div className=\"text-sm text-[var(--soft-gray)]\">{selectedMBTI.description}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* 合作偏好 */}\n        <div className=\"bg-[var(--mist-white)] p-6 rounded-lg\">\n          <div className=\"flex items-center mb-4\">\n            <Heart className=\"w-5 h-5 text-[var(--coral-pink)] mr-2\" />\n            <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)]\">合作偏好</h3>\n          </div>\n          <div className=\"space-y-2 text-sm\">\n            {data.teamSize && <div><strong>团队规模：</strong>{data.teamSize}</div>}\n            {data.communicationStyle && <div><strong>沟通方式：</strong>{data.communicationStyle}</div>}\n            {data.workStyle && data.workStyle.length > 0 && (\n              <div>\n                <strong>工作方式：</strong>\n                <span className=\"ml-1\">{data.workStyle.join(', ')}</span>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 时间投入 */}\n        <div className=\"bg-[var(--mist-white)] p-6 rounded-lg\">\n          <div className=\"flex items-center mb-4\">\n            <Clock className=\"w-5 h-5 text-[var(--ice-blue)] mr-2\" />\n            <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)]\">时间投入</h3>\n          </div>\n          <div className=\"space-y-2 text-sm\">\n            {data.timeAvailable && <div><strong>可投入时间：</strong>{data.timeAvailable}</div>}\n            {data.commitment && <div><strong>承诺程度：</strong>{data.commitment}</div>}\n            {data.timeline && <div><strong>期望时间线：</strong>{data.timeline}</div>}\n          </div>\n        </div>\n\n        {/* 个性标签 */}\n        {allTags.length > 0 && (\n          <div className=\"bg-[var(--mist-white)] p-6 rounded-lg\">\n            <div className=\"flex items-center mb-4\">\n              <Tags className=\"w-5 h-5 text-[var(--mint-green)] mr-2\" />\n              <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)]\">个性标签</h3>\n            </div>\n            <div className=\"flex flex-wrap gap-2\">\n              {allTags.map((tag: string, index: number) => (\n                <span key={index} className=\"px-3 py-1 bg-[var(--coral-pink)] text-white text-sm rounded-full\">\n                  {tag}\n                </span>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 能力雷达图总结 */}\n      {data.radarData && (\n        <div className=\"bg-white p-6 rounded-lg card-shadow\">\n          <div className=\"flex items-center mb-4\">\n            <BarChart3 className=\"w-5 h-5 text-[var(--brand-purple)] mr-2\" />\n            <h3 className=\"text-lg font-semibold text-[var(--dark-blue-gray)]\">能力评估</h3>\n          </div>\n          <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n            {RADAR_DIMENSIONS.map((dim) => (\n              <div key={dim.key} className=\"text-center\">\n                <div className=\"text-2xl font-bold text-[var(--brand-purple)]\">\n                  {data.radarData[dim.key]}\n                </div>\n                <div className=\"text-sm text-[var(--dark-blue-gray)]\">{dim.label}</div>\n              </div>\n            ))}\n          </div>\n          <div className=\"text-center mt-4 pt-4 border-t\">\n            <div className=\"text-sm text-[var(--soft-gray)]\">综合评分</div>\n            <div className=\"text-3xl font-bold text-[var(--coral-pink)]\">{averageScore}</div>\n          </div>\n        </div>\n      )}\n\n      {/* 隐私说明 */}\n      <div className=\"bg-blue-50 border border-blue-200 p-4 rounded-lg\">\n        <h4 className=\"font-semibold text-blue-800 mb-2\">🔒 隐私保护说明</h4>\n        <ul className=\"text-sm text-blue-700 space-y-1\">\n          <li>• 你的个人信息将被安全保护，不会泄露给第三方</li>\n          <li>• 你可以随时修改或删除你的合作画像</li>\n          <li>• 匹配过程中只会显示必要的合作相关信息</li>\n          <li>• 联系方式只有在双方同意后才会共享</li>\n        </ul>\n      </div>\n\n      <div className=\"flex justify-center mt-8\">\n        <Button \n          onClick={handleSubmit}\n          size=\"lg\"\n          disabled={isSubmitting}\n          className=\"px-8\"\n        >\n          {isSubmitting ? '创建中...' : '确认创建合作画像'}\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAee,SAAS,YAAY,EAAE,IAAI,EAAoB;;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,KAAK,QAAQ;IACxE,MAAM,UAAU;WAAK,KAAK,eAAe,IAAI,EAAE;WAAO,KAAK,UAAU,IAAI,EAAE;KAAE;IAC7E,MAAM,eAAe,KAAK,SAAS,GAC/B,CAAC,OAAO,MAAM,CAAC,KAAK,SAAS,EAAE,MAAM,CAAC,CAAC,KAAa,QAAe,MAAM,OAAO,KAAK,CAAC,EAAE,OAAO,CAAC,KAChG;IAEJ,MAAM,eAAe;QACnB,gBAAgB;QAChB,SAAS;QACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,gBAAgB;QAChB,eAAe;IACjB;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;8BAEzB,6LAAC;oBAAG,WAAU;8BAAkD;;;;;;8BAGhE,6LAAC;oBAAE,WAAU;8BAA2C;;;;;;8BAGxD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;0CAAK;;;;;;;;;;;sCAIpB,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;;IAO9C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;;;;;;;0CAErE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAa,KAAK,IAAI;;;;;;;kDACnC,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAa,KAAK,GAAG;;;;;;;kDAClC,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAa,KAAK,MAAM;;;;;;;kDACrC,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAa,KAAK,QAAQ;;;;;;;oCACtC,KAAK,KAAK,kBAAI,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAa,KAAK,KAAK;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;;;;;;;0CAErE,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,mBACnC,6LAAC;;0DACC,6LAAC;gDAAO,WAAU;0DAAU;;;;;;0DAC5B,6LAAC;gDAAI,WAAU;0DACZ,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAe,sBAC/B,6LAAC;wDAAiB,WAAU;kEACzB;uDADQ;;;;;;;;;;;;;;;;oCAOlB,KAAK,OAAO,kBACX,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAO;;;;;;4CAAe,KAAK,OAAO;;;;;;;;;;;;;;;;;;;oBAO1C,8BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;;;;;;;0CAErE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,aAAa,IAAI;;;;;;kDAEpB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAiB,aAAa,IAAI;;;;;;0DACjD,6LAAC;gDAAI,WAAU;0DAAmC,aAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;;kCAOlF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;;;;;;;0CAErE,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,QAAQ,kBAAI,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAe,KAAK,QAAQ;;;;;;;oCAC1D,KAAK,kBAAkB,kBAAI,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAe,KAAK,kBAAkB;;;;;;;oCAC9E,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,mBACzC,6LAAC;;0DACC,6LAAC;0DAAO;;;;;;0DACR,6LAAC;gDAAK,WAAU;0DAAQ,KAAK,SAAS,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAOpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;;;;;;;0CAErE,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,aAAa,kBAAI,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAgB,KAAK,aAAa;;;;;;;oCACrE,KAAK,UAAU,kBAAI,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAe,KAAK,UAAU;;;;;;;oCAC9D,KAAK,QAAQ,kBAAI,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;oBAK/D,QAAQ,MAAM,GAAG,mBAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;;;;;;;0CAErE,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,KAAa,sBACzB,6LAAC;wCAAiB,WAAU;kDACzB;uCADQ;;;;;;;;;;;;;;;;;;;;;;YAUpB,KAAK,SAAS,kBACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAG,WAAU;0CAAqD;;;;;;;;;;;;kCAErE,6LAAC;wBAAI,WAAU;kCACZ,8HAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,oBACrB,6LAAC;gCAAkB,WAAU;;kDAC3B,6LAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,IAAI,GAAG,CAAC;;;;;;kDAE1B,6LAAC;wCAAI,WAAU;kDAAwC,IAAI,KAAK;;;;;;;+BAJxD,IAAI,GAAG;;;;;;;;;;kCAQrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;0CACjD,6LAAC;gCAAI,WAAU;0CAA+C;;;;;;;;;;;;;;;;;;0BAMpE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;0BAIR,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,MAAK;oBACL,UAAU;oBACV,WAAU;8BAET,eAAe,WAAW;;;;;;;;;;;;;;;;;AAKrC;GAvNwB;KAAA", "debugId": null}}, {"offset": {"line": 4406, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/vscode/%E7%BA%BF%E4%B8%8A%E5%8D%8F%E4%BD%9C%E4%B8%8E%E5%88%9B%E4%B8%9A%E6%92%AE%E5%90%88%E5%B9%B3%E5%8F%B0/collaboration-platform/src/app/experience/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { ArrowLeft, ArrowRight } from 'lucide-react'\nimport Link from 'next/link'\n\n// 导入步骤组件\nimport BasicInfoStep from '@/components/experience/BasicInfoStep'\nimport SkillsResourcesStep from '@/components/experience/SkillsResourcesStep'\nimport MBTIStep from '@/components/experience/MBTIStep'\nimport PreferencesStep from '@/components/experience/PreferencesStep'\nimport TimeMotivationStep from '@/components/experience/TimeMotivationStep'\nimport TagsStep from '@/components/experience/TagsStep'\nimport RadarStep from '@/components/experience/RadarStep'\nimport SummaryStep from '@/components/experience/SummaryStep'\n\nconst STEPS = [\n  { id: 'basic', title: '基本信息', component: BasicInfoStep },\n  { id: 'skills', title: '技能资源', component: SkillsResourcesStep },\n  { id: 'mbti', title: 'MBTI测试', component: MBTIStep },\n  { id: 'preferences', title: '合作偏好', component: PreferencesStep },\n  { id: 'time', title: '时间动机', component: TimeMotivationStep },\n  { id: 'tags', title: '标签画像', component: TagsStep },\n  { id: 'radar', title: '能力评估', component: RadarStep },\n  { id: 'summary', title: '信息确认', component: SummaryStep },\n]\n\nexport default function ExperiencePage() {\n  const [currentStep, setCurrentStep] = useState(0)\n  const [formData, setFormData] = useState({})\n\n  const handleNext = () => {\n    if (currentStep < STEPS.length - 1) {\n      setCurrentStep(currentStep + 1)\n    }\n  }\n\n  const handlePrev = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1)\n    }\n  }\n\n  const handleStepData = (stepData: any) => {\n    setFormData(prev => ({ ...prev, ...stepData }))\n  }\n\n  const CurrentStepComponent = STEPS[currentStep].component\n\n  return (\n    <div className=\"min-h-screen gradient-bg\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link href=\"/\" className=\"flex items-center text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]\">\n              <ArrowLeft className=\"w-5 h-5 mr-2\" />\n              返回首页\n            </Link>\n            <div className=\"text-center\">\n              <h1 className=\"text-xl font-semibold text-[var(--dark-blue-gray)]\">\n                创建你的合作画像\n              </h1>\n              <p className=\"text-sm text-[var(--soft-gray)]\">\n                步骤 {currentStep + 1} / {STEPS.length}\n              </p>\n            </div>\n            <div className=\"w-20\"></div> {/* Spacer for centering */}\n          </div>\n        </div>\n      </header>\n\n      {/* Progress Bar */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center py-4\">\n            {STEPS.map((step, index) => (\n              <div key={step.id} className=\"flex items-center flex-1\">\n                <div className=\"flex items-center\">\n                  <div\n                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                      index <= currentStep\n                        ? 'bg-[var(--coral-pink)] text-white'\n                        : 'bg-[var(--mist-white)] text-[var(--soft-gray)]'\n                    }`}\n                  >\n                    {index + 1}\n                  </div>\n                  <span\n                    className={`ml-2 text-sm font-medium ${\n                      index <= currentStep\n                        ? 'text-[var(--dark-blue-gray)]'\n                        : 'text-[var(--soft-gray)]'\n                    }`}\n                  >\n                    {step.title}\n                  </span>\n                </div>\n                {index < STEPS.length - 1 && (\n                  <div\n                    className={`flex-1 h-1 mx-4 rounded ${\n                      index < currentStep\n                        ? 'bg-[var(--coral-pink)]'\n                        : 'bg-[var(--mist-white)]'\n                    }`}\n                  />\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-xl card-shadow p-8\">\n          <CurrentStepComponent\n            data={formData}\n            onDataChange={handleStepData}\n            onNext={handleNext}\n            onPrev={handlePrev}\n          />\n        </div>\n\n        {/* Navigation */}\n        <div className=\"flex justify-between mt-8\">\n          <Button\n            variant=\"outline\"\n            onClick={handlePrev}\n            disabled={currentStep === 0}\n            className=\"flex items-center\"\n          >\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            上一步\n          </Button>\n          \n          <Button\n            onClick={handleNext}\n            disabled={currentStep === STEPS.length - 1}\n            className=\"flex items-center\"\n          >\n            下一步\n            <ArrowRight className=\"w-4 h-4 ml-2\" />\n          </Button>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAEA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;;AAiBA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAS,OAAO;QAAQ,WAAW,oJAAA,CAAA,UAAa;IAAC;IACvD;QAAE,IAAI;QAAU,OAAO;QAAQ,WAAW,0JAAA,CAAA,UAAmB;IAAC;IAC9D;QAAE,IAAI;QAAQ,OAAO;QAAU,WAAW,+IAAA,CAAA,UAAQ;IAAC;IACnD;QAAE,IAAI;QAAe,OAAO;QAAQ,WAAW,sJAAA,CAAA,UAAe;IAAC;IAC/D;QAAE,IAAI;QAAQ,OAAO;QAAQ,WAAW,yJAAA,CAAA,UAAkB;IAAC;IAC3D;QAAE,IAAI;QAAQ,OAAO;QAAQ,WAAW,+IAAA,CAAA,UAAQ;IAAC;IACjD;QAAE,IAAI;QAAS,OAAO;QAAQ,WAAW,gJAAA,CAAA,UAAS;IAAC;IACnD;QAAE,IAAI;QAAW,OAAO;QAAQ,WAAW,kJAAA,CAAA,UAAW;IAAC;CACxD;AAEc,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAE1C,MAAM,aAAa;QACjB,IAAI,cAAc,MAAM,MAAM,GAAG,GAAG;YAClC,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,QAAQ;YAAC,CAAC;IAC/C;IAEA,MAAM,uBAAuB,KAAK,CAAC,YAAY,CAAC,SAAS;IAEzD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,6LAAC;wCAAE,WAAU;;4CAAkC;4CACzC,cAAc;4CAAE;4CAAI,MAAM,MAAM;;;;;;;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;;;;;4BAAa;;;;;;;;;;;;;;;;;0BAMlC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAAkB,WAAU;;kDAC3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,0EAA0E,EACpF,SAAS,cACL,sCACA,kDACJ;0DAED,QAAQ;;;;;;0DAEX,6LAAC;gDACC,WAAW,CAAC,yBAAyB,EACnC,SAAS,cACL,iCACA,2BACJ;0DAED,KAAK,KAAK;;;;;;;;;;;;oCAGd,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC;wCACC,WAAW,CAAC,wBAAwB,EAClC,QAAQ,cACJ,2BACA,0BACJ;;;;;;;+BA3BE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;0BAqCzB,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAM;4BACN,cAAc;4BACd,QAAQ;4BACR,QAAQ;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,gBAAgB;gCAC1B,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIxC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,gBAAgB,MAAM,MAAM,GAAG;gCACzC,WAAU;;oCACX;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;GAzHwB;KAAA", "debugId": null}}]}