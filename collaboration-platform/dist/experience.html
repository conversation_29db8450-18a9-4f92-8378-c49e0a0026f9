<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/569ce4b8f30dc480-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/93f479601ee12b01-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/9959d14f85dbef39.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad967276a8926d54.js"/><script src="/_next/static/chunks/4bd1b696-a06c382ed846a660.js" async=""></script><script src="/_next/static/chunks/303-30c1863249c8355b.js" async=""></script><script src="/_next/static/chunks/main-app-7c2357ce8f18aebe.js" async=""></script><script src="/_next/static/chunks/178-65bdbde0b09b5f77.js" async=""></script><script src="/_next/static/chunks/18-18e419319ac4f485.js" async=""></script><script src="/_next/static/chunks/app/experience/page-9645436daae76c85.js" async=""></script><meta name="next-size-adjust" content=""/><title>Create Next App</title><meta name="description" content="Generated by create next app"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div class="min-h-screen gradient-bg"><header class="bg-white shadow-sm"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><div class="flex items-center justify-between"><a class="flex items-center text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-5 h-5 mr-2" aria-hidden="true"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg>返回首页</a><div class="text-center"><h1 class="text-xl font-semibold text-[var(--dark-blue-gray)]">创建你的合作画像</h1><p class="text-sm text-[var(--soft-gray)]">步骤 <!-- -->1<!-- --> / <!-- -->8</p></div><div class="w-20"></div> </div></div></header><div class="bg-white border-b"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex items-center py-4"><div class="flex items-center flex-1"><div class="flex items-center"><div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-[var(--coral-pink)] text-white">1</div><span class="ml-2 text-sm font-medium text-[var(--dark-blue-gray)]">基本信息</span></div><div class="flex-1 h-1 mx-4 rounded bg-[var(--mist-white)]"></div></div><div class="flex items-center flex-1"><div class="flex items-center"><div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-[var(--mist-white)] text-[var(--soft-gray)]">2</div><span class="ml-2 text-sm font-medium text-[var(--soft-gray)]">技能资源</span></div><div class="flex-1 h-1 mx-4 rounded bg-[var(--mist-white)]"></div></div><div class="flex items-center flex-1"><div class="flex items-center"><div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-[var(--mist-white)] text-[var(--soft-gray)]">3</div><span class="ml-2 text-sm font-medium text-[var(--soft-gray)]">MBTI测试</span></div><div class="flex-1 h-1 mx-4 rounded bg-[var(--mist-white)]"></div></div><div class="flex items-center flex-1"><div class="flex items-center"><div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-[var(--mist-white)] text-[var(--soft-gray)]">4</div><span class="ml-2 text-sm font-medium text-[var(--soft-gray)]">合作偏好</span></div><div class="flex-1 h-1 mx-4 rounded bg-[var(--mist-white)]"></div></div><div class="flex items-center flex-1"><div class="flex items-center"><div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-[var(--mist-white)] text-[var(--soft-gray)]">5</div><span class="ml-2 text-sm font-medium text-[var(--soft-gray)]">时间动机</span></div><div class="flex-1 h-1 mx-4 rounded bg-[var(--mist-white)]"></div></div><div class="flex items-center flex-1"><div class="flex items-center"><div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-[var(--mist-white)] text-[var(--soft-gray)]">6</div><span class="ml-2 text-sm font-medium text-[var(--soft-gray)]">标签画像</span></div><div class="flex-1 h-1 mx-4 rounded bg-[var(--mist-white)]"></div></div><div class="flex items-center flex-1"><div class="flex items-center"><div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-[var(--mist-white)] text-[var(--soft-gray)]">7</div><span class="ml-2 text-sm font-medium text-[var(--soft-gray)]">能力评估</span></div><div class="flex-1 h-1 mx-4 rounded bg-[var(--mist-white)]"></div></div><div class="flex items-center flex-1"><div class="flex items-center"><div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-[var(--mist-white)] text-[var(--soft-gray)]">8</div><span class="ml-2 text-sm font-medium text-[var(--soft-gray)]">信息确认</span></div></div></div></div></div><main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="bg-white rounded-xl card-shadow p-8"><div class="space-y-6"><div class="text-center mb-8"><h2 class="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">基本信息</h2><p class="text-[var(--soft-gray)]">让我们先了解一下你的基本情况</p></div><div class="grid md:grid-cols-2 gap-6"><div><label class="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">昵称 *</label><input type="text" class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent border-[var(--soft-gray)]" placeholder="请输入你的昵称" value=""/></div><div><label class="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">年龄段 *</label><select class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent border-[var(--soft-gray)]"><option value="" selected="">请选择年龄段</option><option value="18-22">18-22岁</option><option value="23-27">23-27岁</option><option value="28-32">28-32岁</option><option value="33-37">33-37岁</option><option value="38-42">38-42岁</option><option value="43+">43岁以上</option></select></div><div><label class="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">性别 *</label><div class="flex space-x-4"><label class="flex items-center"><input type="radio" class="mr-2 text-[var(--coral-pink)] focus:ring-[var(--coral-pink)]" name="gender" value="男"/><span class="text-[var(--dark-blue-gray)]">男</span></label><label class="flex items-center"><input type="radio" class="mr-2 text-[var(--coral-pink)] focus:ring-[var(--coral-pink)]" name="gender" value="女"/><span class="text-[var(--dark-blue-gray)]">女</span></label><label class="flex items-center"><input type="radio" class="mr-2 text-[var(--coral-pink)] focus:ring-[var(--coral-pink)]" name="gender" value="其他"/><span class="text-[var(--dark-blue-gray)]">其他</span></label></div></div><div><label class="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">所在地区 *</label><input type="text" class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent border-[var(--soft-gray)]" placeholder="如：北京、上海、深圳" value=""/></div><div><label class="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">邮箱邮箱（可选）</label><input type="email" class="w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent" placeholder="<EMAIL>" value=""/></div><div><label class="block text-sm font-medium text-[var(--dark-blue-gray)] mb-2">Telegram联系方式（可选）</label><input type="text" class="w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent" placeholder="@username 或 Telegram用户名" value=""/></div></div><div class="flex justify-end mt-8"><button class="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-6 py-3 text-lg bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)]">下一步：技能资源</button></div></div></div><div class="flex justify-between mt-8"><button class="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2 text-base border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)] flex items-center" disabled=""><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-4 h-4 mr-2" aria-hidden="true"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg>上一步</button><button class="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2 text-base bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)] flex items-center">下一步<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 ml-2" aria-hidden="true"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></button></div></main></div><!--$--><!--/$--><!--$--><!--/$--><script src="/_next/static/chunks/webpack-ad967276a8926d54.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[87555,[],\"\"]\n3:I[31295,[],\"\"]\n4:I[90894,[],\"ClientPageRoot\"]\n5:I[10849,[\"178\",\"static/chunks/178-65bdbde0b09b5f77.js\",\"18\",\"static/chunks/18-18e419319ac4f485.js\",\"127\",\"static/chunks/app/experience/page-9645436daae76c85.js\"],\"default\"]\n8:I[59665,[],\"MetadataBoundary\"]\na:I[59665,[],\"OutletBoundary\"]\nd:I[74911,[],\"AsyncMetadataOutlet\"]\nf:I[59665,[],\"ViewportBoundary\"]\n11:I[26614,[],\"\"]\n:HL[\"/_next/static/media/569ce4b8f30dc480-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/93f479601ee12b01-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/9959d14f85dbef39.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"lsj633YmXqIjOh0wWFUgA\",\"p\":\"\",\"c\":[\"\",\"experience\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"experience\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/9959d14f85dbef39.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"experience\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"dII196p0VLeSixGRQKojV\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[74911,[],\"AsyncMetadata\"]\n6:{}\n7:{}\n9:[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Create Next App\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Generated by create next app\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\ne:{\"metadata\":\"$14:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>