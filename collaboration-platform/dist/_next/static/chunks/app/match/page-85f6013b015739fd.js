(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[684],{2869:(e,s,a)=>{"use strict";a.d(s,{$:()=>r});var t=a(95155),i=a(52596);let r=(0,a(12115).forwardRef)((e,s)=>{let{className:a,variant:r="primary",size:l="md",...c}=e;return(0,t.jsx)("button",{className:function(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,i.$)(s)}("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{"px-3 py-1.5 text-sm":"sm"===l,"px-4 py-2 text-base":"md"===l,"px-6 py-3 text-lg":"lg"===l},{"bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)]":"primary"===r,"bg-[var(--mint-green)] text-white hover:bg-[#16a085] focus:ring-[var(--mint-green)]":"secondary"===r,"bg-[var(--brand-purple)] text-white hover:bg-[#7d3c98] focus:ring-[var(--brand-purple)]":"accent"===r,"border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]":"outline"===r,"text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)] focus:ring-[var(--soft-gray)]":"ghost"===r},a),ref:s,...c})});r.displayName="Button"},14186:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},16785:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17580:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},17618:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var t=a(95155),i=a(12115),r=a(2869),l=a(57647),c=a(19946);let n=(0,c.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var d=a(17580),m=a(16785),x=a(71007);let o=(0,c.A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);var h=a(51976),p=a(14186);let v=(0,c.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var g=a(6874),b=a.n(g);function u(){let[e,s]=(0,i.useState)("people"),[a,c]=(0,i.useState)(!1),g=l.o1.slice(1),u=l.ku;return(0,t.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm",children:(0,t.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(b(),{href:"/",className:"text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]",children:"← 返回首页"}),(0,t.jsx)("h1",{className:"text-xl font-semibold text-[var(--dark-blue-gray)]",children:"智能匹配推荐"}),(0,t.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>c(!a),children:[(0,t.jsx)(n,{className:"w-4 h-4 mr-2"}),"筛选"]})]})})}),(0,t.jsxs)("main",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-8",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-1 shadow-sm",children:[(0,t.jsxs)("button",{onClick:()=>s("people"),className:"px-6 py-2 rounded-md font-medium transition-all ".concat("people"===e?"bg-[var(--coral-pink)] text-white":"text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)]"),children:[(0,t.jsx)(d.A,{className:"w-4 h-4 inline mr-2"}),"合作伙伴"]}),(0,t.jsxs)("button",{onClick:()=>s("projects"),className:"px-6 py-2 rounded-md font-medium transition-all ".concat("projects"===e?"bg-[var(--coral-pink)] text-white":"text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)]"),children:[(0,t.jsx)(m.A,{className:"w-4 h-4 inline mr-2"}),"推荐项目"]})]})}),"people"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"为你推荐的合作伙伴"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)]",children:"基于你的技能、性格和偏好，我们为你找到了这些潜在的合作伙伴"})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:g.map(e=>{let s=l.Fr.find(s=>s.code===e.mbti);return(0,t.jsxs)("div",{className:"bg-white rounded-xl card-shadow card-hover p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] rounded-full flex items-center justify-center mr-3",children:(0,t.jsx)(x.A,{className:"w-6 h-6 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-[var(--dark-blue-gray)]",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-[var(--soft-gray)]",children:[e.age,"岁 \xb7 ",e.location]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold text-[var(--mint-green)]",children:[e.compatibility,"%"]}),(0,t.jsx)("div",{className:"text-xs text-[var(--soft-gray)]",children:"匹配度"})]})]}),s&&(0,t.jsx)("div",{className:"bg-[var(--mist-white)] p-3 rounded-lg mb-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"font-bold text-[var(--brand-purple)] mr-2",children:s.code}),(0,t.jsx)("span",{className:"text-sm text-[var(--dark-blue-gray)]",children:s.name})]})}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"核心技能"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.skills.map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 bg-[var(--coral-pink)] text-white text-xs rounded",children:e},s))})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"个性特质"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded",children:e},s))})]}),(0,t.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg mb-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-blue-800 mb-1",children:"匹配原因"}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:"技能互补度高 \xb7 性格类型匹配 \xb7 工作方式相似"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(r.$,{size:"sm",className:"flex-1",children:[(0,t.jsx)(o,{className:"w-4 h-4 mr-1"}),"发起聊天"]}),(0,t.jsx)(r.$,{variant:"outline",size:"sm",children:(0,t.jsx)(h.A,{className:"w-4 h-4"})})]})]},e.id)})})]}),"projects"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"推荐项目"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)]",children:"根据你的技能和兴趣，这些项目可能很适合你参与"})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:u.map(e=>(0,t.jsxs)("div",{className:"bg-white rounded-xl card-shadow card-hover p-6",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-2",children:e.title}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)] text-sm mb-3",children:e.description})]}),(0,t.jsxs)("div",{className:"text-right ml-4",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold text-[var(--mint-green)]",children:[e.compatibility,"%"]}),(0,t.jsx)("div",{className:"text-xs text-[var(--soft-gray)]",children:"匹配度"})]})]}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map((e,s)=>(0,t.jsx)("span",{className:"px-3 py-1 bg-[var(--ice-blue)] text-white text-sm rounded-full",children:e},s))})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"w-4 h-4 text-[var(--soft-gray)] mr-2"}),(0,t.jsxs)("span",{className:"text-sm",children:["团队 ",e.teamSize," 人"]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 text-[var(--soft-gray)] mr-2"}),(0,t.jsxs)("span",{className:"text-sm",children:["进度 ",e.progress,"%"]})]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{className:"text-[var(--dark-blue-gray)]",children:"项目进度"}),(0,t.jsxs)("span",{className:"text-[var(--soft-gray)]",children:[e.progress,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-[var(--mist-white)] rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-[var(--mint-green)] h-2 rounded-full transition-all",style:{width:"".concat(e.progress,"%")}})})]}),(0,t.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg mb-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-green-800 mb-1",children:"推荐原因"}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"技能需求匹配 \xb7 项目类型符合兴趣 \xb7 团队规模合适"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(r.$,{size:"sm",className:"flex-1",children:"申请加入"}),(0,t.jsx)(r.$,{variant:"outline",size:"sm",children:(0,t.jsx)(v,{className:"w-4 h-4"})})]})]},e.id))})]}),(0,t.jsx)("div",{className:"mt-12 text-center",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 card-shadow",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-2",children:"没有找到合适的匹配？"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)] mb-4",children:"我们会持续为你寻找新的合作机会，也可以完善你的画像信息来获得更精准的推荐"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,t.jsx)(b(),{href:"/profile",children:(0,t.jsx)(r.$,{variant:"outline",children:"完善画像信息"})}),(0,t.jsx)(r.$,{children:"设置推荐提醒"})]})]})})]})]})}},51976:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},57647:(e,s,a)=>{"use strict";a.d(s,{Fr:()=>i,RW:()=>r,ku:()=>c,o1:()=>l,uf:()=>t});let t={skills:["前端开发","后端开发","产品设计","用户体验","数据分析","市场营销","商业策划","项目管理","内容创作","视觉设计"],personality:["团队协作","独立思考","创新思维","执行力强","沟通能力强","学习能力强","抗压能力强","领导力","细心负责","目标导向"],workStyle:["远程工作","线下协作","灵活时间","规律作息","快速迭代","深度思考","数据驱动","用户导向","技术驱动","商业导向"],interests:["人工智能","区块链","电商平台","教育科技","健康医疗","金融科技","社交网络","游戏娱乐","企业服务","生活服务"]},i=[{code:"INTJ",name:"建筑师",description:"富有想象力和战略性的思想家"},{code:"INTP",name:"思想家",description:"具有创造性的发明家"},{code:"ENTJ",name:"指挥官",description:"大胆、富有想象力、意志强烈的领导者"},{code:"ENTP",name:"辩论家",description:"聪明好奇的思想家"},{code:"INFJ",name:"提倡者",description:"安静而神秘的理想主义者"},{code:"INFP",name:"调停者",description:"诗意、善良的利他主义者"},{code:"ENFJ",name:"主人公",description:"富有魅力、鼓舞人心的领导者"},{code:"ENFP",name:"竞选者",description:"热情、有创造力的自由精神"},{code:"ISTJ",name:"物流师",description:"实用主义的事实导向者"},{code:"ISFJ",name:"守护者",description:"非常专注、温暖的守护者"},{code:"ESTJ",name:"总经理",description:"出色的管理者"},{code:"ESFJ",name:"执政官",description:"极有同情心、受欢迎的人"},{code:"ISTP",name:"鉴赏家",description:"大胆而实际的实验者"},{code:"ISFP",name:"探险家",description:"灵活、有魅力的艺术家"},{code:"ESTP",name:"企业家",description:"聪明、精力充沛的感知者"},{code:"ESFP",name:"娱乐家",description:"自发的、精力充沛的娱乐者"}],r=[{key:"communication",label:"沟通力",description:"表达和理解他人的能力"},{key:"execution",label:"执行力",description:"将想法转化为行动的能力"},{key:"empathy",label:"共情力",description:"理解和感受他人情感的能力"},{key:"analysis",label:"分析力",description:"逻辑思维和问题解决能力"},{key:"learning",label:"学习力",description:"快速掌握新知识和技能的能力"},{key:"leadership",label:"领导力",description:"影响和激励他人的能力"}],l=[{id:"1",name:"张小明",age:28,location:"北京",mbti:"INTJ",skills:["前端开发","产品设计"],tags:["团队协作","创新思维","远程工作"],radarData:{communication:8,execution:9,empathy:6,analysis:9,learning:8,leadership:7},compatibility:92},{id:"2",name:"李小红",age:25,location:"上海",mbti:"ENFP",skills:["市场营销","内容创作"],tags:["沟通能力强","创新思维","用户导向"],radarData:{communication:9,execution:7,empathy:9,analysis:6,learning:8,leadership:8},compatibility:87},{id:"3",name:"王小华",age:30,location:"深圳",mbti:"ESTJ",skills:["项目管理","商业策划"],tags:["执行力强","领导力","目标导向"],radarData:{communication:8,execution:9,empathy:7,analysis:8,learning:7,leadership:9},compatibility:85}],c=[{id:"1",title:"AI驱动的学习平台",description:"基于人工智能的个性化学习推荐系统",tags:["人工智能","教育科技","前端开发"],teamSize:4,progress:30,compatibility:94},{id:"2",title:"可持续生活社区",description:"连接环保爱好者的社交平台",tags:["社交网络","生活服务","用户体验"],teamSize:3,progress:15,compatibility:89}]},71007:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},81148:(e,s,a)=>{Promise.resolve().then(a.bind(a,17618))}},e=>{var s=s=>e(e.s=s);e.O(0,[178,441,303,358],()=>s(81148)),_N_E=e.O()}]);