(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{676:(e,s,a)=>{Promise.resolve().then(a.bind(a,2991))},2869:(e,s,a)=>{"use strict";a.d(s,{$:()=>r});var i=a(95155),l=a(52596);let r=(0,a(12115).forwardRef)((e,s)=>{let{className:a,variant:r="primary",size:t="md",...c}=e;return(0,i.jsx)("button",{className:function(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,l.$)(s)}("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{"px-3 py-1.5 text-sm":"sm"===t,"px-4 py-2 text-base":"md"===t,"px-6 py-3 text-lg":"lg"===t},{"bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)]":"primary"===r,"bg-[var(--mint-green)] text-white hover:bg-[#16a085] focus:ring-[var(--mint-green)]":"secondary"===r,"bg-[var(--brand-purple)] text-white hover:bg-[#7d3c98] focus:ring-[var(--brand-purple)]":"accent"===r,"border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]":"outline"===r,"text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)] focus:ring-[var(--soft-gray)]":"ghost"===r},a),ref:s,...c})});r.displayName="Button"},2991:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>f});var i=a(95155),l=a(2869),r=a(57647),t=a(19946);let c=(0,t.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),n=(0,t.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var d=a(71007);let m=(0,t.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),x=(0,t.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var o=a(83540),h=a(80930),p=a(68238),v=a(11832),g=a(57313),j=a(89473),u=a(6874),N=a.n(u);function f(){let e=r.o1[0],s=r.Fr.find(s=>s.code===e.mbti),a=r.RW.map(s=>({dimension:s.label,value:e.radarData[s.key],fullMark:10}));return(0,i.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,i.jsx)("header",{className:"bg-white shadow-sm",children:(0,i.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)(N(),{href:"/",className:"text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]",children:"← 返回首页"}),(0,i.jsx)("h1",{className:"text-xl font-semibold text-[var(--dark-blue-gray)]",children:"我的合作画像"}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,i.jsx)(c,{className:"w-4 h-4 mr-2"}),"编辑"]}),(0,i.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,i.jsx)(n,{className:"w-4 h-4 mr-2"}),"分享"]})]})]})})}),(0,i.jsx)("main",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,i.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,i.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,i.jsxs)("div",{className:"bg-white rounded-xl card-shadow p-6",children:[(0,i.jsxs)("div",{className:"text-center mb-6",children:[(0,i.jsx)("div",{className:"w-20 h-20 bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)(d.A,{className:"w-10 h-10 text-white"})}),(0,i.jsx)("h2",{className:"text-xl font-bold text-[var(--dark-blue-gray)]",children:e.name}),(0,i.jsxs)("p",{className:"text-[var(--soft-gray)]",children:[e.age,"岁"]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center text-sm",children:[(0,i.jsx)(m,{className:"w-4 h-4 text-[var(--soft-gray)] mr-2"}),(0,i.jsx)("span",{children:e.location})]}),(0,i.jsxs)("div",{className:"flex items-center text-sm",children:[(0,i.jsx)(x,{className:"w-4 h-4 text-[var(--soft-gray)] mr-2"}),(0,i.jsx)("span",{children:"<EMAIL>"})]})]})]}),s&&(0,i.jsxs)("div",{className:"bg-gradient-to-r from-[var(--brand-purple)] to-[var(--ice-blue)] rounded-xl p-6 text-white",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"MBTI性格类型"}),(0,i.jsxs)("div",{className:"flex items-center mb-3",children:[(0,i.jsx)("div",{className:"text-3xl font-bold mr-4",children:s.code}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-semibold",children:s.name}),(0,i.jsx)("div",{className:"text-sm opacity-90",children:s.description})]})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-xl card-shadow p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"核心技能"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:e.skills.map((e,s)=>(0,i.jsx)("span",{className:"px-3 py-1 bg-[var(--coral-pink)] text-white text-sm rounded-full",children:e},s))})]}),(0,i.jsxs)("div",{className:"bg-white rounded-xl card-shadow p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"个性标签"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map((e,s)=>(0,i.jsx)("span",{className:"px-3 py-1 bg-[var(--mint-green)] text-white text-sm rounded-full",children:e},s))})]})]}),(0,i.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,i.jsxs)("div",{className:"bg-white rounded-xl card-shadow p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-6",children:"六维能力评估"}),(0,i.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8",children:[(0,i.jsx)("div",{className:"h-80",children:(0,i.jsx)(o.u,{width:"100%",height:"100%",children:(0,i.jsxs)(h.V,{data:a,children:[(0,i.jsx)(p.z,{}),(0,i.jsx)(v.r,{dataKey:"dimension",tick:{fontSize:12,fill:"#7F8C8D"}}),(0,i.jsx)(g.E,{angle:90,domain:[0,10],tick:{fontSize:10,fill:"#BDC3C7"}}),(0,i.jsx)(j.V,{name:"能力值",dataKey:"value",stroke:"#FF6B6B",fill:"#FF6B6B",fillOpacity:.3,strokeWidth:2})]})})}),(0,i.jsxs)("div",{className:"space-y-4",children:[r.RW.map(s=>(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-[var(--dark-blue-gray)]",children:s.label}),(0,i.jsx)("div",{className:"text-sm text-[var(--soft-gray)]",children:s.description})]}),(0,i.jsx)("div",{className:"text-2xl font-bold text-[var(--coral-pink)]",children:e.radarData[s.key]})]},s.key)),(0,i.jsx)("div",{className:"pt-4 border-t",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-sm text-[var(--soft-gray)]",children:"综合评分"}),(0,i.jsx)("div",{className:"text-3xl font-bold text-[var(--coral-pink)]",children:(Object.values(e.radarData).reduce((e,s)=>e+s,0)/6).toFixed(1)})]})})]})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-xl card-shadow p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-6",children:"合作偏好"}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-[var(--dark-blue-gray)] mb-3",children:"工作方式"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3"}),(0,i.jsx)("span",{className:"text-sm",children:"远程工作"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3"}),(0,i.jsx)("span",{className:"text-sm",children:"灵活时间"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3"}),(0,i.jsx)("span",{className:"text-sm",children:"深度思考"})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-[var(--dark-blue-gray)] mb-3",children:"团队偏好"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3"}),(0,i.jsx)("span",{className:"text-sm",children:"2-3人小团队"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3"}),(0,i.jsx)("span",{className:"text-sm",children:"技术导向"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3"}),(0,i.jsx)("span",{className:"text-sm",children:"中度承诺"})]})]})]})]})]}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,i.jsx)(N(),{href:"/match",className:"flex-1",children:(0,i.jsx)(l.$,{size:"lg",className:"w-full",children:"查看匹配推荐"})}),(0,i.jsx)(N(),{href:"/experience",className:"flex-1",children:(0,i.jsx)(l.$,{variant:"outline",size:"lg",className:"w-full",children:"更新画像信息"})})]})]})]})})]})}},57647:(e,s,a)=>{"use strict";a.d(s,{Fr:()=>l,RW:()=>r,ku:()=>c,o1:()=>t,uf:()=>i});let i={skills:["前端开发","后端开发","产品设计","用户体验","数据分析","市场营销","商业策划","项目管理","内容创作","视觉设计"],personality:["团队协作","独立思考","创新思维","执行力强","沟通能力强","学习能力强","抗压能力强","领导力","细心负责","目标导向"],workStyle:["远程工作","线下协作","灵活时间","规律作息","快速迭代","深度思考","数据驱动","用户导向","技术驱动","商业导向"],interests:["人工智能","区块链","电商平台","教育科技","健康医疗","金融科技","社交网络","游戏娱乐","企业服务","生活服务"]},l=[{code:"INTJ",name:"建筑师",description:"富有想象力和战略性的思想家"},{code:"INTP",name:"思想家",description:"具有创造性的发明家"},{code:"ENTJ",name:"指挥官",description:"大胆、富有想象力、意志强烈的领导者"},{code:"ENTP",name:"辩论家",description:"聪明好奇的思想家"},{code:"INFJ",name:"提倡者",description:"安静而神秘的理想主义者"},{code:"INFP",name:"调停者",description:"诗意、善良的利他主义者"},{code:"ENFJ",name:"主人公",description:"富有魅力、鼓舞人心的领导者"},{code:"ENFP",name:"竞选者",description:"热情、有创造力的自由精神"},{code:"ISTJ",name:"物流师",description:"实用主义的事实导向者"},{code:"ISFJ",name:"守护者",description:"非常专注、温暖的守护者"},{code:"ESTJ",name:"总经理",description:"出色的管理者"},{code:"ESFJ",name:"执政官",description:"极有同情心、受欢迎的人"},{code:"ISTP",name:"鉴赏家",description:"大胆而实际的实验者"},{code:"ISFP",name:"探险家",description:"灵活、有魅力的艺术家"},{code:"ESTP",name:"企业家",description:"聪明、精力充沛的感知者"},{code:"ESFP",name:"娱乐家",description:"自发的、精力充沛的娱乐者"}],r=[{key:"communication",label:"沟通力",description:"表达和理解他人的能力"},{key:"execution",label:"执行力",description:"将想法转化为行动的能力"},{key:"empathy",label:"共情力",description:"理解和感受他人情感的能力"},{key:"analysis",label:"分析力",description:"逻辑思维和问题解决能力"},{key:"learning",label:"学习力",description:"快速掌握新知识和技能的能力"},{key:"leadership",label:"领导力",description:"影响和激励他人的能力"}],t=[{id:"1",name:"张小明",age:28,location:"北京",mbti:"INTJ",skills:["前端开发","产品设计"],tags:["团队协作","创新思维","远程工作"],radarData:{communication:8,execution:9,empathy:6,analysis:9,learning:8,leadership:7},compatibility:92},{id:"2",name:"李小红",age:25,location:"上海",mbti:"ENFP",skills:["市场营销","内容创作"],tags:["沟通能力强","创新思维","用户导向"],radarData:{communication:9,execution:7,empathy:9,analysis:6,learning:8,leadership:8},compatibility:87},{id:"3",name:"王小华",age:30,location:"深圳",mbti:"ESTJ",skills:["项目管理","商业策划"],tags:["执行力强","领导力","目标导向"],radarData:{communication:8,execution:9,empathy:7,analysis:8,learning:7,leadership:9},compatibility:85}],c=[{id:"1",title:"AI驱动的学习平台",description:"基于人工智能的个性化学习推荐系统",tags:["人工智能","教育科技","前端开发"],teamSize:4,progress:30,compatibility:94},{id:"2",title:"可持续生活社区",description:"连接环保爱好者的社交平台",tags:["社交网络","生活服务","用户体验"],teamSize:3,progress:15,compatibility:89}]}},e=>{var s=s=>e(e.s=s);e.O(0,[178,18,441,303,358],()=>s(676)),_N_E=e.O()}]);