(()=>{var e={};e.id=636,e.ids=[636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8751:(e,s,a)=>{"use strict";a.d(s,{Fr:()=>t,RW:()=>i,ku:()=>n,o1:()=>l,uf:()=>r});let r={skills:["前端开发","后端开发","产品设计","用户体验","数据分析","市场营销","商业策划","项目管理","内容创作","视觉设计"],personality:["团队协作","独立思考","创新思维","执行力强","沟通能力强","学习能力强","抗压能力强","领导力","细心负责","目标导向"],workStyle:["远程工作","线下协作","灵活时间","规律作息","快速迭代","深度思考","数据驱动","用户导向","技术驱动","商业导向"],interests:["人工智能","区块链","电商平台","教育科技","健康医疗","金融科技","社交网络","游戏娱乐","企业服务","生活服务"]},t=[{code:"INTJ",name:"建筑师",description:"富有想象力和战略性的思想家"},{code:"INTP",name:"思想家",description:"具有创造性的发明家"},{code:"ENTJ",name:"指挥官",description:"大胆、富有想象力、意志强烈的领导者"},{code:"ENTP",name:"辩论家",description:"聪明好奇的思想家"},{code:"INFJ",name:"提倡者",description:"安静而神秘的理想主义者"},{code:"INFP",name:"调停者",description:"诗意、善良的利他主义者"},{code:"ENFJ",name:"主人公",description:"富有魅力、鼓舞人心的领导者"},{code:"ENFP",name:"竞选者",description:"热情、有创造力的自由精神"},{code:"ISTJ",name:"物流师",description:"实用主义的事实导向者"},{code:"ISFJ",name:"守护者",description:"非常专注、温暖的守护者"},{code:"ESTJ",name:"总经理",description:"出色的管理者"},{code:"ESFJ",name:"执政官",description:"极有同情心、受欢迎的人"},{code:"ISTP",name:"鉴赏家",description:"大胆而实际的实验者"},{code:"ISFP",name:"探险家",description:"灵活、有魅力的艺术家"},{code:"ESTP",name:"企业家",description:"聪明、精力充沛的感知者"},{code:"ESFP",name:"娱乐家",description:"自发的、精力充沛的娱乐者"}],i=[{key:"communication",label:"沟通力",description:"表达和理解他人的能力"},{key:"execution",label:"执行力",description:"将想法转化为行动的能力"},{key:"empathy",label:"共情力",description:"理解和感受他人情感的能力"},{key:"analysis",label:"分析力",description:"逻辑思维和问题解决能力"},{key:"learning",label:"学习力",description:"快速掌握新知识和技能的能力"},{key:"leadership",label:"领导力",description:"影响和激励他人的能力"}],l=[{id:"1",name:"张小明",age:28,location:"北京",mbti:"INTJ",skills:["前端开发","产品设计"],tags:["团队协作","创新思维","远程工作"],radarData:{communication:8,execution:9,empathy:6,analysis:9,learning:8,leadership:7},compatibility:92},{id:"2",name:"李小红",age:25,location:"上海",mbti:"ENFP",skills:["市场营销","内容创作"],tags:["沟通能力强","创新思维","用户导向"],radarData:{communication:9,execution:7,empathy:9,analysis:6,learning:8,leadership:8},compatibility:87},{id:"3",name:"王小华",age:30,location:"深圳",mbti:"ESTJ",skills:["项目管理","商业策划"],tags:["执行力强","领导力","目标导向"],radarData:{communication:8,execution:9,empathy:7,analysis:8,learning:7,leadership:9},compatibility:85}],n=[{id:"1",title:"AI驱动的学习平台",description:"基于人工智能的个性化学习推荐系统",tags:["人工智能","教育科技","前端开发"],teamSize:4,progress:30,compatibility:94},{id:"2",title:"可持续生活社区",description:"连接环保爱好者的社交平台",tags:["社交网络","生活服务","用户体验"],teamSize:3,progress:15,compatibility:89}]},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32667:()=>{},33873:e=>{"use strict";e.exports=require("path")},35168:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},35596:(e,s,a)=>{Promise.resolve().then(a.bind(a,45654))},45654:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>j});var r=a(60687),t=a(49699),i=a(8751),l=a(62688);let n=(0,l.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),d=(0,l.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var o=a(58869);let c=(0,l.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),m=(0,l.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var x=a(48482),p=a(75969),h=a(61276),v=a(87780),u=a(7515),b=a(57176),f=a(85814),g=a.n(f);function j(){let e=i.o1[0],s=i.Fr.find(s=>s.code===e.mbti),a=i.RW.map(s=>({dimension:s.label,value:e.radarData[s.key],fullMark:10}));return(0,r.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(g(),{href:"/",className:"text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]",children:"← 返回首页"}),(0,r.jsx)("h1",{className:"text-xl font-semibold text-[var(--dark-blue-gray)]",children:"我的合作画像"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(t.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(n,{className:"w-4 h-4 mr-2"}),"编辑"]}),(0,r.jsxs)(t.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(d,{className:"w-4 h-4 mr-2"}),"分享"]})]})]})})}),(0,r.jsx)("main",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl card-shadow p-6",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsx)("div",{className:"w-20 h-20 bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(o.A,{className:"w-10 h-10 text-white"})}),(0,r.jsx)("h2",{className:"text-xl font-bold text-[var(--dark-blue-gray)]",children:e.name}),(0,r.jsxs)("p",{className:"text-[var(--soft-gray)]",children:[e.age,"岁"]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)(c,{className:"w-4 h-4 text-[var(--soft-gray)] mr-2"}),(0,r.jsx)("span",{children:e.location})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)(m,{className:"w-4 h-4 text-[var(--soft-gray)] mr-2"}),(0,r.jsx)("span",{children:"<EMAIL>"})]})]})]}),s&&(0,r.jsxs)("div",{className:"bg-gradient-to-r from-[var(--brand-purple)] to-[var(--ice-blue)] rounded-xl p-6 text-white",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"MBTI性格类型"}),(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)("div",{className:"text-3xl font-bold mr-4",children:s.code}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-semibold",children:s.name}),(0,r.jsx)("div",{className:"text-sm opacity-90",children:s.description})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl card-shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"核心技能"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.skills.map((e,s)=>(0,r.jsx)("span",{className:"px-3 py-1 bg-[var(--coral-pink)] text-white text-sm rounded-full",children:e},s))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl card-shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"个性标签"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map((e,s)=>(0,r.jsx)("span",{className:"px-3 py-1 bg-[var(--mint-green)] text-white text-sm rounded-full",children:e},s))})]})]}),(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl card-shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-6",children:"六维能力评估"}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8",children:[(0,r.jsx)("div",{className:"h-80",children:(0,r.jsx)(x.u,{width:"100%",height:"100%",children:(0,r.jsxs)(p.V,{data:a,children:[(0,r.jsx)(h.z,{}),(0,r.jsx)(v.r,{dataKey:"dimension",tick:{fontSize:12,fill:"#7F8C8D"}}),(0,r.jsx)(u.E,{angle:90,domain:[0,10],tick:{fontSize:10,fill:"#BDC3C7"}}),(0,r.jsx)(b.V,{name:"能力值",dataKey:"value",stroke:"#FF6B6B",fill:"#FF6B6B",fillOpacity:.3,strokeWidth:2})]})})}),(0,r.jsxs)("div",{className:"space-y-4",children:[i.RW.map(s=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-[var(--dark-blue-gray)]",children:s.label}),(0,r.jsx)("div",{className:"text-sm text-[var(--soft-gray)]",children:s.description})]}),(0,r.jsx)("div",{className:"text-2xl font-bold text-[var(--coral-pink)]",children:e.radarData[s.key]})]},s.key)),(0,r.jsx)("div",{className:"pt-4 border-t",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-sm text-[var(--soft-gray)]",children:"综合评分"}),(0,r.jsx)("div",{className:"text-3xl font-bold text-[var(--coral-pink)]",children:(Object.values(e.radarData).reduce((e,s)=>e+s,0)/6).toFixed(1)})]})})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl card-shadow p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-6",children:"合作偏好"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-[var(--dark-blue-gray)] mb-3",children:"工作方式"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3"}),(0,r.jsx)("span",{className:"text-sm",children:"远程工作"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3"}),(0,r.jsx)("span",{className:"text-sm",children:"灵活时间"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-[var(--mint-green)] rounded-full mr-3"}),(0,r.jsx)("span",{className:"text-sm",children:"深度思考"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-[var(--dark-blue-gray)] mb-3",children:"团队偏好"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3"}),(0,r.jsx)("span",{className:"text-sm",children:"2-3人小团队"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3"}),(0,r.jsx)("span",{className:"text-sm",children:"技术导向"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-[var(--coral-pink)] rounded-full mr-3"}),(0,r.jsx)("span",{className:"text-sm",children:"中度承诺"})]})]})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)(g(),{href:"/match",className:"flex-1",children:(0,r.jsx)(t.$,{size:"lg",className:"w-full",children:"查看匹配推荐"})}),(0,r.jsx)(g(),{href:"/experience",className:"flex-1",children:(0,r.jsx)(t.$,{variant:"outline",size:"lg",className:"w-full",children:"更新画像信息"})})]})]})]})})]})}},45868:(e,s,a)=>{Promise.resolve().then(a.bind(a,75758))},49699:(e,s,a)=>{"use strict";a.d(s,{$:()=>i});var r=a(60687),t=a(49384);let i=(0,a(43210).forwardRef)(({className:e,variant:s="primary",size:a="md",...i},l)=>(0,r.jsx)("button",{className:function(...e){return(0,t.$)(e)}("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{"px-3 py-1.5 text-sm":"sm"===a,"px-4 py-2 text-base":"md"===a,"px-6 py-3 text-lg":"lg"===a},{"bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)]":"primary"===s,"bg-[var(--mint-green)] text-white hover:bg-[#16a085] focus:ring-[var(--mint-green)]":"secondary"===s,"bg-[var(--brand-purple)] text-white hover:bg-[#7d3c98] focus:ring-[var(--brand-purple)]":"accent"===s,"border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]":"outline"===s,"text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)] focus:ring-[var(--soft-gray)]":"ghost"===s},e),ref:l,...i}));i.displayName="Button"},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66096:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=a(65239),t=a(48088),i=a(88170),l=a.n(i),n=a(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let o={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,75758)),"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/profile/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/profile/page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},69459:()=>{},70440:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});var r=a(31658);let t=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71616:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},75758:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/profile/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/profile/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},94431:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o,metadata:()=>d});var r=a(37413),t=a(2202),i=a.n(t),l=a(64988),n=a.n(l);a(61135);let d={title:"Create Next App",description:"Generated by create next app"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().variable} ${n().variable} antialiased`,children:e})})}}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[447,169,167,747],()=>a(66096));module.exports=r})();