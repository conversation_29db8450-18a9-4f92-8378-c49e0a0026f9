(()=>{var e={};e.id=127,e.ids=[127],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8751:(e,s,a)=>{"use strict";a.d(s,{Fr:()=>r,RW:()=>l,ku:()=>n,o1:()=>i,uf:()=>t});let t={skills:["前端开发","后端开发","产品设计","用户体验","数据分析","市场营销","商业策划","项目管理","内容创作","视觉设计"],personality:["团队协作","独立思考","创新思维","执行力强","沟通能力强","学习能力强","抗压能力强","领导力","细心负责","目标导向"],workStyle:["远程工作","线下协作","灵活时间","规律作息","快速迭代","深度思考","数据驱动","用户导向","技术驱动","商业导向"],interests:["人工智能","区块链","电商平台","教育科技","健康医疗","金融科技","社交网络","游戏娱乐","企业服务","生活服务"]},r=[{code:"INTJ",name:"建筑师",description:"富有想象力和战略性的思想家"},{code:"INTP",name:"思想家",description:"具有创造性的发明家"},{code:"ENTJ",name:"指挥官",description:"大胆、富有想象力、意志强烈的领导者"},{code:"ENTP",name:"辩论家",description:"聪明好奇的思想家"},{code:"INFJ",name:"提倡者",description:"安静而神秘的理想主义者"},{code:"INFP",name:"调停者",description:"诗意、善良的利他主义者"},{code:"ENFJ",name:"主人公",description:"富有魅力、鼓舞人心的领导者"},{code:"ENFP",name:"竞选者",description:"热情、有创造力的自由精神"},{code:"ISTJ",name:"物流师",description:"实用主义的事实导向者"},{code:"ISFJ",name:"守护者",description:"非常专注、温暖的守护者"},{code:"ESTJ",name:"总经理",description:"出色的管理者"},{code:"ESFJ",name:"执政官",description:"极有同情心、受欢迎的人"},{code:"ISTP",name:"鉴赏家",description:"大胆而实际的实验者"},{code:"ISFP",name:"探险家",description:"灵活、有魅力的艺术家"},{code:"ESTP",name:"企业家",description:"聪明、精力充沛的感知者"},{code:"ESFP",name:"娱乐家",description:"自发的、精力充沛的娱乐者"}],l=[{key:"communication",label:"沟通力",description:"表达和理解他人的能力"},{key:"execution",label:"执行力",description:"将想法转化为行动的能力"},{key:"empathy",label:"共情力",description:"理解和感受他人情感的能力"},{key:"analysis",label:"分析力",description:"逻辑思维和问题解决能力"},{key:"learning",label:"学习力",description:"快速掌握新知识和技能的能力"},{key:"leadership",label:"领导力",description:"影响和激励他人的能力"}],i=[{id:"1",name:"张小明",age:28,location:"北京",mbti:"INTJ",skills:["前端开发","产品设计"],tags:["团队协作","创新思维","远程工作"],radarData:{communication:8,execution:9,empathy:6,analysis:9,learning:8,leadership:7},compatibility:92},{id:"2",name:"李小红",age:25,location:"上海",mbti:"ENFP",skills:["市场营销","内容创作"],tags:["沟通能力强","创新思维","用户导向"],radarData:{communication:9,execution:7,empathy:9,analysis:6,learning:8,leadership:8},compatibility:87},{id:"3",name:"王小华",age:30,location:"深圳",mbti:"ESTJ",skills:["项目管理","商业策划"],tags:["执行力强","领导力","目标导向"],radarData:{communication:8,execution:9,empathy:7,analysis:8,learning:7,leadership:9},compatibility:85}],n=[{id:"1",title:"AI驱动的学习平台",description:"基于人工智能的个性化学习推荐系统",tags:["人工智能","教育科技","前端开发"],teamSize:4,progress:30,compatibility:94},{id:"2",title:"可持续生活社区",description:"连接环保爱好者的社交平台",tags:["社交网络","生活服务","用户体验"],teamSize:3,progress:15,compatibility:89}]},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28947:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32667:()=>{},33873:e=>{"use strict";e.exports=require("path")},35168:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},48730:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49445:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>D});var t=a(60687),r=a(43210),l=a(49699),i=a(28559),n=a(62688);let d=(0,n.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var c=a(85814),o=a.n(c),m=a(8751);let x=(0,n.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),h=(0,n.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),p=(0,n.A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),g=(0,n.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var v=a(5336),u=a(48730),b=a(28947),j=a(67760),y=a(48482),f=a(75969),N=a(61276),k=a(87780),w=a(7515),T=a(57176),S=a(58869);let C=(0,n.A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]),P=(0,n.A)("tags",[["path",{d:"m15 5 6.3 6.3a2.4 2.4 0 0 1 0 3.4L17 19",key:"1cbfv1"}],["path",{d:"M9.586 5.586A2 2 0 0 0 8.172 5H3a1 1 0 0 0-1 1v5.172a2 2 0 0 0 .586 1.414L8.29 18.29a2.426 2.426 0 0 0 3.42 0l3.58-3.58a2.426 2.426 0 0 0 0-3.42z",key:"135mg7"}],["circle",{cx:"6.5",cy:"9.5",r:".5",fill:"currentColor",key:"5pm5xn"}]]),A=(0,n.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),$=[{id:"basic",title:"基本信息",component:function({data:e,onDataChange:s,onNext:a}){let[i,n]=(0,r.useState)({name:e.name||"",age:e.age||"",gender:e.gender||"",location:e.location||"",email:e.email||"",telegram:e.telegram||""}),[d,c]=(0,r.useState)({}),o=(e,s)=>{n(a=>({...a,[e]:s})),d[e]&&c(s=>({...s,[e]:""}))},m=()=>{let e={};return i.name.trim()||(e.name="请输入昵称"),i.age||(e.age="请选择年龄段"),i.gender||(e.gender="请选择性别"),i.location.trim()||(e.location="请输入所在地区"),c(e),0===Object.keys(e).length};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"基本信息"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)]",children:"让我们先了解一下你的基本情况"})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"昵称 *"}),(0,t.jsx)("input",{type:"text",value:i.name,onChange:e=>o("name",e.target.value),className:`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent ${d.name?"border-red-500":"border-[var(--soft-gray)]"}`,placeholder:"请输入你的昵称"}),d.name&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-500",children:d.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"年龄段 *"}),(0,t.jsxs)("select",{value:i.age,onChange:e=>o("age",e.target.value),className:`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent ${d.age?"border-red-500":"border-[var(--soft-gray)]"}`,children:[(0,t.jsx)("option",{value:"",children:"请选择年龄段"}),(0,t.jsx)("option",{value:"18-22",children:"18-22岁"}),(0,t.jsx)("option",{value:"23-27",children:"23-27岁"}),(0,t.jsx)("option",{value:"28-32",children:"28-32岁"}),(0,t.jsx)("option",{value:"33-37",children:"33-37岁"}),(0,t.jsx)("option",{value:"38-42",children:"38-42岁"}),(0,t.jsx)("option",{value:"43+",children:"43岁以上"})]}),d.age&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-500",children:d.age})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"性别 *"}),(0,t.jsx)("div",{className:"flex space-x-4",children:["男","女","其他"].map(e=>(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"radio",name:"gender",value:e,checked:i.gender===e,onChange:e=>o("gender",e.target.value),className:"mr-2 text-[var(--coral-pink)] focus:ring-[var(--coral-pink)]"}),(0,t.jsx)("span",{className:"text-[var(--dark-blue-gray)]",children:e})]},e))}),d.gender&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-500",children:d.gender})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"所在地区 *"}),(0,t.jsx)("input",{type:"text",value:i.location,onChange:e=>o("location",e.target.value),className:`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent ${d.location?"border-red-500":"border-[var(--soft-gray)]"}`,placeholder:"如：北京、上海、深圳"}),d.location&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-500",children:d.location})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"邮箱邮箱（可选）"}),(0,t.jsx)("input",{type:"email",value:i.email,onChange:e=>o("email",e.target.value),className:"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent",placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"Telegram联系方式（可选）"}),(0,t.jsx)("input",{type:"text",value:i.telegram,onChange:e=>o("telegram",e.target.value),className:"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent",placeholder:"@username 或 Telegram用户名"})]})]}),(0,t.jsx)("div",{className:"flex justify-end mt-8",children:(0,t.jsx)(l.$,{onClick:()=>{m()&&a()},size:"lg",children:"下一步：技能资源"})})]})}},{id:"skills",title:"技能资源",component:function({data:e,onDataChange:s,onNext:a}){let[i,n]=(0,r.useState)({skills:e.skills||[],resources:e.resources||[],ideas:e.ideas||"",funding:e.funding||"",customSkill:"",customResource:""}),d=e=>{n(s=>({...s,skills:s.skills.includes(e)?s.skills.filter(s=>s!==e):[...s.skills,e]}))},c=()=>{i.customSkill.trim()&&!i.skills.includes(i.customSkill.trim())&&n(e=>({...e,skills:[...e.skills,e.customSkill.trim()],customSkill:""}))},o=(e,s)=>{n(a=>({...a,resources:{...a.resources,[e]:s}}))},p=e=>{n(s=>({...s,skills:s.skills.filter(s=>s!==e)}))};return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"技能与资源"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)]",children:"告诉我们你拥有的技能和可以提供的资源"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"你的技能专长"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mb-4",children:m.uf.skills.map(e=>(0,t.jsx)("button",{onClick:()=>d(e),className:`px-4 py-2 rounded-lg text-sm font-medium transition-all ${i.skills.includes(e)?"bg-[var(--coral-pink)] text-white":"bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white"}`,children:e},e))}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("input",{type:"text",value:i.customSkill,onChange:e=>n(s=>({...s,customSkill:e.target.value})),placeholder:"添加其他技能...",className:"flex-1 px-4 py-2 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&c()}),(0,t.jsx)(l.$,{onClick:c,variant:"outline",size:"sm",children:(0,t.jsx)(x,{className:"w-4 h-4"})})]}),i.skills.length>0&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("p",{className:"text-sm text-[var(--soft-gray)] mb-2",children:"已选择的技能："}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:i.skills.map(e=>(0,t.jsxs)("span",{className:"inline-flex items-center px-3 py-1 bg-[var(--coral-pink)] text-white text-sm rounded-full",children:[e,(0,t.jsx)("button",{onClick:()=>p(e),className:"ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-0.5",children:(0,t.jsx)(h,{className:"w-3 h-3"})})]},e))})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"创意想法"}),(0,t.jsx)("textarea",{value:i.ideas,onChange:e=>n(s=>({...s,ideas:e.target.value})),rows:4,className:"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent",placeholder:"描述你的项目想法或创意概念..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"资金情况"}),(0,t.jsxs)("select",{value:i.funding,onChange:e=>n(s=>({...s,funding:e.target.value})),className:"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent",children:[(0,t.jsx)("option",{value:"",children:"选择资金情况"}),(0,t.jsx)("option",{value:"none",children:"暂无资金"}),(0,t.jsx)("option",{value:"small",children:"少量资金（1-5万）"}),(0,t.jsx)("option",{value:"medium",children:"中等资金（5-20万）"}),(0,t.jsx)("option",{value:"large",children:"充足资金（20万以上）"}),(0,t.jsx)("option",{value:"seeking",children:"正在寻求投资"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"其他可提供的资源"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"人脉网络"}),(0,t.jsx)("input",{type:"text",value:i.resources.network||"",onChange:e=>o("network",e.target.value),className:"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent",placeholder:"如：投资人、行业专家、技术大牛等"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"设备工具"}),(0,t.jsx)("input",{type:"text",value:i.resources.equipment||"",onChange:e=>o("equipment",e.target.value),className:"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent",placeholder:"如：服务器、设计软件、办公场地等"})]})]})]}),(0,t.jsx)("div",{className:"flex justify-end mt-8",children:(0,t.jsx)(l.$,{onClick:a,size:"lg",children:"下一步：MBTI测试"})})]})}},{id:"mbti",title:"MBTI测试",component:function({data:e,onDataChange:s,onNext:a}){let[i,n]=(0,r.useState)({mbtiType:e.mbtiType||"",mbtiSource:e.mbtiSource||"",quickTestAnswers:e.quickTestAnswers||{}}),[d,c]=(0,r.useState)(!1),[o,x]=(0,r.useState)(0),h=[{question:"在聚会中，你更倾向于：",options:[{text:"与少数几个人深入交谈",type:"I"},{text:"与很多人轻松聊天",type:"E"}]},{question:"做决定时，你更依赖：",options:[{text:"逻辑分析和客观事实",type:"T"},{text:"个人价值观和他人感受",type:"F"}]},{question:"你更喜欢：",options:[{text:"制定详细计划并按计划执行",type:"J"},{text:"保持灵活性，随机应变",type:"P"}]},{question:"学习新知识时，你更关注：",options:[{text:"具体的事实和细节",type:"S"},{text:"整体概念和可能性",type:"N"}]}],u=e=>{n(s=>({...s,mbtiType:e,mbtiSource:"direct"}))},b=(e,s)=>{let a={...i.quickTestAnswers,[o]:{answer:e,type:s}};if(n(e=>({...e,quickTestAnswers:a})),o<h.length-1)x(o+1);else{let e={E:0,I:0,S:0,N:0,T:0,F:0,J:0,P:0};Object.values(a).forEach(s=>{e[s.type]++});let s=(e.E>e.I?"E":"I")+(e.S>e.N?"S":"N")+(e.T>e.F?"T":"F")+(e.J>e.P?"J":"P");n(e=>({...e,mbtiType:s,mbtiSource:"quickTest"})),c(!1),x(0)}},j=m.Fr.find(e=>e.code===i.mbtiType);if(d){let e=h[o];return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"快速MBTI测试"}),(0,t.jsxs)("p",{className:"text-[var(--soft-gray)]",children:["问题 ",o+1," / ",h.length]})]}),(0,t.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg mb-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:e.question}),(0,t.jsx)("div",{className:"space-y-3",children:e.options.map((e,s)=>(0,t.jsx)("button",{onClick:()=>b(e.text,e.type),className:"w-full p-4 text-left bg-white rounded-lg border border-[var(--soft-gray)] hover:border-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white transition-all",children:e.text},s))})]})})]})}return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"MBTI性格类型"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)]",children:"选择获取MBTI结果的方式"})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg text-center",children:[(0,t.jsx)(p,{className:"w-12 h-12 text-[var(--coral-pink)] mx-auto mb-4"}),(0,t.jsx)("h3",{className:"font-semibold text-[var(--dark-blue-gray)] mb-2",children:"我已知道结果"}),(0,t.jsx)("p",{className:"text-sm text-[var(--soft-gray)] mb-4",children:"直接选择你的MBTI类型"}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>n(e=>({...e,mbtiSource:"direct"})),children:"直接选择"})]}),(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg text-center",children:[(0,t.jsx)(g,{className:"w-12 h-12 text-[var(--mint-green)] mx-auto mb-4"}),(0,t.jsx)("h3",{className:"font-semibold text-[var(--dark-blue-gray)] mb-2",children:"专业测试"}),(0,t.jsx)("p",{className:"text-sm text-[var(--soft-gray)] mb-4",children:"跳转到专业MBTI测试网站"}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>window.open("https://www.16personalities.com/ch","_blank"),children:"去测试"})]}),(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg text-center",children:[(0,t.jsx)(v.A,{className:"w-12 h-12 text-[var(--brand-purple)] mx-auto mb-4"}),(0,t.jsx)("h3",{className:"font-semibold text-[var(--dark-blue-gray)] mb-2",children:"快速测试"}),(0,t.jsx)("p",{className:"text-sm text-[var(--soft-gray)] mb-4",children:"4道题快速了解你的类型"}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{c(!0),x(0),n(e=>({...e,quickTestAnswers:{}}))},children:"开始测试"})]})]}),"direct"===i.mbtiSource&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"选择你的MBTI类型"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:m.Fr.map(e=>(0,t.jsxs)("button",{onClick:()=>u(e.code),className:`p-4 rounded-lg border text-left transition-all ${i.mbtiType===e.code?"border-[var(--coral-pink)] bg-[var(--coral-pink)] text-white":"border-[var(--soft-gray)] hover:border-[var(--coral-pink)]"}`,children:[(0,t.jsx)("div",{className:"font-bold text-lg",children:e.code}),(0,t.jsx)("div",{className:"text-sm opacity-90",children:e.name})]},e.code))})]}),j&&(0,t.jsx)("div",{className:"bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] p-6 rounded-lg text-white",children:(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"text-3xl font-bold mr-4",children:j.code}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-semibold",children:j.name}),(0,t.jsx)("p",{className:"opacity-90",children:j.description})]})]})}),(0,t.jsx)("div",{className:"flex justify-end mt-8",children:(0,t.jsx)(l.$,{onClick:a,size:"lg",disabled:!i.mbtiType,children:"下一步：合作偏好"})})]})}},{id:"preferences",title:"合作偏好",component:function({data:e,onDataChange:s,onNext:a}){let[i,n]=(0,r.useState)({workStyle:e.workStyle||[],teamSize:e.teamSize||"",communicationStyle:e.communicationStyle||"",projectType:e.projectType||[],avoidTypes:e.avoidTypes||[],idealPartner:e.idealPartner||""}),d=e=>{n(s=>({...s,workStyle:s.workStyle.includes(e)?s.workStyle.filter(s=>s!==e):[...s.workStyle,e]}))},c=e=>{n(s=>({...s,projectType:s.projectType.includes(e)?s.projectType.filter(s=>s!==e):[...s.projectType,e]}))},o=e=>{n(s=>({...s,avoidTypes:s.avoidTypes.includes(e)?s.avoidTypes.filter(s=>s!==e):[...s.avoidTypes,e]}))};return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"合作偏好"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)]",children:"告诉我们你喜欢的合作方式和项目类型"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"工作方式偏好"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:m.uf.workStyle.map(e=>(0,t.jsx)("button",{onClick:()=>d(e),className:`px-4 py-3 rounded-lg text-sm font-medium transition-all ${i.workStyle.includes(e)?"bg-[var(--mint-green)] text-white":"bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--mint-green)] hover:text-white"}`,children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"理想团队规模"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:["2-3人小团队","4-6人中团队","7-10人大团队","10人以上"].map(e=>(0,t.jsx)("button",{onClick:()=>n(s=>({...s,teamSize:e})),className:`px-4 py-3 rounded-lg text-sm font-medium transition-all ${i.teamSize===e?"bg-[var(--coral-pink)] text-white":"bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white"}`,children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"偏好的沟通方式"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:["面对面深度讨论","在线视频会议","文字消息沟通","定期汇报总结","随时随地交流","结构化会议"].map(e=>(0,t.jsx)("button",{onClick:()=>n(s=>({...s,communicationStyle:e})),className:`px-4 py-3 rounded-lg text-sm font-medium transition-all text-left ${i.communicationStyle===e?"bg-[var(--brand-purple)] text-white":"bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--brand-purple)] hover:text-white"}`,children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"感兴趣的项目类型"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:m.uf.interests.map(e=>(0,t.jsx)("button",{onClick:()=>c(e),className:`px-4 py-3 rounded-lg text-sm font-medium transition-all ${i.projectType.includes(e)?"bg-[var(--ice-blue)] text-white":"bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--ice-blue)] hover:text-white"}`,children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"不太适合的合作类型（可选）"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:["高压力快节奏","需要频繁出差","长期加班项目","高风险投资","纯技术导向","纯商业导向"].map(e=>(0,t.jsx)("button",{onClick:()=>o(e),className:`px-4 py-3 rounded-lg text-sm font-medium transition-all text-left ${i.avoidTypes.includes(e)?"bg-red-500 text-white":"bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-red-500 hover:text-white"}`,children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"理想合作伙伴描述（可选）"}),(0,t.jsx)("textarea",{value:i.idealPartner,onChange:e=>n(s=>({...s,idealPartner:e.target.value})),rows:4,className:"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent",placeholder:"描述你理想中的合作伙伴特质、工作风格等..."})]}),(0,t.jsx)("div",{className:"flex justify-end mt-8",children:(0,t.jsx)(l.$,{onClick:a,size:"lg",children:"下一步：时间与动机"})})]})}},{id:"time",title:"时间动机",component:function({data:e,onDataChange:s,onNext:a}){let[i,n]=(0,r.useState)({timeAvailable:e.timeAvailable||"",timePreference:e.timePreference||"",commitment:e.commitment||"",motivation:e.motivation||[],goals:e.goals||"",timeline:e.timeline||""}),d=e=>{n(s=>({...s,motivation:s.motivation.includes(e)?s.motivation.filter(s=>s!==e):[...s.motivation,e]}))};return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"时间投入与动机"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)]",children:"了解你的时间安排和参与动机"})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(u.A,{className:"w-5 h-5 text-[var(--coral-pink)] mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)]",children:"每周可投入时间"})]}),(0,t.jsx)("div",{className:"space-y-3",children:["5小时以下（业余时间）","5-15小时（兼职投入）","15-30小时（半职投入）","30小时以上（全职投入）"].map(e=>(0,t.jsx)("button",{onClick:()=>n(s=>({...s,timeAvailable:e})),className:`w-full px-4 py-3 rounded-lg text-left transition-all ${i.timeAvailable===e?"bg-[var(--coral-pink)] text-white":"bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white"}`,children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(b.A,{className:"w-5 h-5 text-[var(--mint-green)] mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)]",children:"时间偏好"})]}),(0,t.jsx)("div",{className:"space-y-3",children:["工作日晚上","周末集中时间","灵活安排","固定时间段"].map(e=>(0,t.jsx)("button",{onClick:()=>n(s=>({...s,timePreference:e})),className:`w-full px-4 py-3 rounded-lg text-left transition-all ${i.timePreference===e?"bg-[var(--mint-green)] text-white":"bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--mint-green)] hover:text-white"}`,children:e},e))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"项目承诺程度"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[{level:"轻度参与",desc:"尝试性参与，可随时退出",color:"var(--ice-blue)"},{level:"中度承诺",desc:"认真参与，但保持灵活性",color:"var(--mint-green)"},{level:"高度承诺",desc:"全力投入，长期合作",color:"var(--coral-pink)"}].map(e=>(0,t.jsxs)("button",{onClick:()=>n(s=>({...s,commitment:e.level})),className:`p-4 rounded-lg border-2 text-left transition-all ${i.commitment===e.level?`border-[${e.color}] bg-[${e.color}] text-white`:"border-[var(--soft-gray)] hover:border-[var(--coral-pink)]"}`,children:[(0,t.jsx)("div",{className:"font-semibold mb-2",children:e.level}),(0,t.jsx)("div",{className:"text-sm opacity-90",children:e.desc})]},e.level))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(j.A,{className:"w-5 h-5 text-[var(--brand-purple)] mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)]",children:"参与动机（可多选）"})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3",children:["学习新技能","获得收入","积累经验","扩展人脉","实现创意","解决问题","帮助他人","个人成长","行业影响力","创业梦想"].map(e=>(0,t.jsx)("button",{onClick:()=>d(e),className:`px-3 py-2 rounded-lg text-sm font-medium transition-all ${i.motivation.includes(e)?"bg-[var(--brand-purple)] text-white":"bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--brand-purple)] hover:text-white"}`,children:e},e))})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"个人目标"}),(0,t.jsx)("textarea",{value:i.goals,onChange:e=>n(s=>({...s,goals:e.target.value})),rows:4,className:"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent",placeholder:"描述你希望通过合作实现的个人目标..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"期望时间线"}),(0,t.jsxs)("select",{value:i.timeline,onChange:e=>n(s=>({...s,timeline:e.target.value})),className:"w-full px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent mb-4",children:[(0,t.jsx)("option",{value:"",children:"选择项目时间线"}),(0,t.jsx)("option",{value:"1-3months",children:"1-3个月短期项目"}),(0,t.jsx)("option",{value:"3-6months",children:"3-6个月中期项目"}),(0,t.jsx)("option",{value:"6-12months",children:"6-12个月长期项目"}),(0,t.jsx)("option",{value:"1year+",children:"1年以上持续项目"})]}),(0,t.jsxs)("div",{className:"text-sm text-[var(--soft-gray)] bg-[var(--mist-white)] p-3 rounded-lg",children:["\uD83D\uDCA1 ",(0,t.jsx)("strong",{children:"提示："}),"明确的时间预期有助于找到合适的合作伙伴"]})]})]}),(0,t.jsx)("div",{className:"flex justify-end mt-8",children:(0,t.jsx)(l.$,{onClick:a,size:"lg",children:"下一步：标签画像"})})]})}},{id:"tags",title:"标签画像",component:function({data:e,onDataChange:s,onNext:a}){let[i,n]=(0,r.useState)({personalityTags:e.personalityTags||[],customTags:e.customTags||[],newTag:""}),d=e=>{n(s=>({...s,personalityTags:s.personalityTags.includes(e)?s.personalityTags.filter(s=>s!==e):[...s.personalityTags,e]}))},c=()=>{i.newTag.trim()&&!i.customTags.includes(i.newTag.trim())&&n(e=>({...e,customTags:[...e.customTags,e.newTag.trim()],newTag:""}))},o=e=>{n(s=>({...s,customTags:s.customTags.filter(s=>s!==e)}))},p=[...i.personalityTags,...i.customTags];return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"个性标签画像"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)]",children:"选择最能代表你的标签，构建你的个性画像"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"性格特质"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3",children:m.uf.personality.map(e=>(0,t.jsx)("button",{onClick:()=>d(e),className:`px-4 py-3 rounded-lg text-sm font-medium transition-all ${i.personalityTags.includes(e)?"bg-[var(--coral-pink)] text-white":"bg-[var(--mist-white)] text-[var(--dark-blue-gray)] hover:bg-[var(--coral-pink)] hover:text-white"}`,children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"自定义标签"}),(0,t.jsxs)("div",{className:"flex gap-2 mb-4",children:[(0,t.jsx)("input",{type:"text",value:i.newTag,onChange:e=>n(s=>({...s,newTag:e.target.value})),placeholder:"添加个性化标签...",className:"flex-1 px-4 py-3 border border-[var(--soft-gray)] rounded-lg focus:ring-2 focus:ring-[var(--coral-pink)] focus:border-transparent",onKeyPress:e=>"Enter"===e.key&&c()}),(0,t.jsx)(l.$,{onClick:c,variant:"outline",children:(0,t.jsx)(x,{className:"w-4 h-4"})})]}),i.customTags.length>0&&(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:i.customTags.map(e=>(0,t.jsxs)("span",{className:"inline-flex items-center px-3 py-1 bg-[var(--mint-green)] text-white text-sm rounded-full",children:[e,(0,t.jsx)("button",{onClick:()=>o(e),className:"ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-0.5",children:(0,t.jsx)(h,{className:"w-3 h-3"})})]},e))})]}),p.length>0&&(0,t.jsxs)("div",{className:"bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] p-6 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"你的个性画像预览"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:p.map((e,s)=>(0,t.jsx)("span",{className:"px-3 py-1 bg-white bg-opacity-20 text-white text-sm rounded-full",children:e},s))}),(0,t.jsxs)("p",{className:"text-white text-sm mt-4 opacity-90",children:["已选择 ",p.length," 个标签"]})]}),(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-3",children:"\uD83D\uDCA1 标签选择建议"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm text-[var(--soft-gray)]",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{className:"text-[var(--dark-blue-gray)]",children:"选择原则："}),(0,t.jsxs)("ul",{className:"mt-2 space-y-1",children:[(0,t.jsx)("li",{children:"• 选择5-8个最能代表你的标签"}),(0,t.jsx)("li",{children:"• 包含工作风格和性格特质"}),(0,t.jsx)("li",{children:"• 避免选择过多相似标签"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{className:"text-[var(--dark-blue-gray)]",children:"标签作用："}),(0,t.jsxs)("ul",{className:"mt-2 space-y-1",children:[(0,t.jsx)("li",{children:"• 帮助他人快速了解你"}),(0,t.jsx)("li",{children:"• 提高匹配准确度"}),(0,t.jsx)("li",{children:"• 展示你的独特性"})]})]})]})]}),(0,t.jsx)("div",{className:"flex justify-end mt-8",children:(0,t.jsx)(l.$,{onClick:a,size:"lg",disabled:0===p.length,children:"下一步：能力评估"})})]})}},{id:"radar",title:"能力评估",component:function({data:e,onDataChange:s,onNext:a}){let[i,n]=(0,r.useState)({radarData:e.radarData||m.RW.reduce((e,s)=>({...e,[s.key]:5}),{})}),d=(e,s)=>{n(a=>({...a,radarData:{...a.radarData,[e]:s}}))},c=m.RW.map(e=>({dimension:e.label,value:i.radarData[e.key]||5,fullMark:10})),o=e=>e<=3?"初级":e<=6?"中级":e<=8?"高级":"专家",x=e=>e<=3?"#ff6b6b":e<=6?"#ffa726":e<=8?"#66bb6a":"#42a5f5";return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"六维能力评估"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)]",children:"诚实评估你在各个维度的能力水平"})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8",children:[(0,t.jsx)("div",{className:"space-y-6",children:m.RW.map(e=>(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-4 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-[var(--dark-blue-gray)]",children:e.label}),(0,t.jsx)("p",{className:"text-sm text-[var(--soft-gray)]",children:e.description})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-2xl font-bold",style:{color:x(i.radarData[e.key])},children:i.radarData[e.key]}),(0,t.jsx)("div",{className:"text-xs text-[var(--soft-gray)]",children:o(i.radarData[e.key])})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("input",{type:"range",min:"1",max:"10",value:i.radarData[e.key],onChange:s=>d(e.key,parseInt(s.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider",style:{background:`linear-gradient(to right, ${x(i.radarData[e.key])} 0%, ${x(i.radarData[e.key])} ${10*i.radarData[e.key]}%, #e5e7eb ${10*i.radarData[e.key]}%, #e5e7eb 100%)`}}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-[var(--soft-gray)]",children:[(0,t.jsx)("span",{children:"1 (初学)"}),(0,t.jsx)("span",{children:"5 (中等)"}),(0,t.jsx)("span",{children:"10 (专家)"})]})]})]},e.key))}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg card-shadow",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4 text-center",children:"能力雷达图"}),(0,t.jsx)("div",{className:"h-80",children:(0,t.jsx)(y.u,{width:"100%",height:"100%",children:(0,t.jsxs)(f.V,{data:c,children:[(0,t.jsx)(N.z,{}),(0,t.jsx)(k.r,{dataKey:"dimension",tick:{fontSize:12,fill:"#7F8C8D"}}),(0,t.jsx)(w.E,{angle:90,domain:[0,10],tick:{fontSize:10,fill:"#BDC3C7"}}),(0,t.jsx)(T.V,{name:"能力值",dataKey:"value",stroke:"#FF6B6B",fill:"#FF6B6B",fillOpacity:.3,strokeWidth:2})]})})}),(0,t.jsxs)("div",{className:"mt-4 text-center",children:[(0,t.jsx)("div",{className:"text-sm text-[var(--soft-gray)] mb-2",children:"综合能力评分"}),(0,t.jsx)("div",{className:"text-2xl font-bold text-[var(--coral-pink)]",children:(Object.values(i.radarData).reduce((e,s)=>e+s,0)/6).toFixed(1)}),(0,t.jsx)("div",{className:"text-xs text-[var(--soft-gray)]",children:"/ 10.0"})]})]})]}),(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-4",children:"\uD83D\uDCCA 评分参考指南"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-[var(--dark-blue-gray)] mb-2",children:"评分标准："}),(0,t.jsxs)("ul",{className:"space-y-1 text-[var(--soft-gray)]",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"1-3分："})," 初学者水平，需要学习和提升"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"4-6分："})," 中等水平，有一定经验"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"7-8分："})," 高级水平，经验丰富"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"9-10分："})," 专家水平，可以指导他人"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-[var(--dark-blue-gray)] mb-2",children:"诚实评估的重要性："}),(0,t.jsxs)("ul",{className:"space-y-1 text-[var(--soft-gray)]",children:[(0,t.jsx)("li",{children:"• 帮助找到互补的合作伙伴"}),(0,t.jsx)("li",{children:"• 避免能力不匹配的问题"}),(0,t.jsx)("li",{children:"• 建立真实可信的合作关系"}),(0,t.jsx)("li",{children:"• 促进团队协作效率"})]})]})]})]}),(0,t.jsx)("div",{className:"flex justify-end mt-8",children:(0,t.jsx)(l.$,{onClick:a,size:"lg",children:"下一步：信息确认"})})]})}},{id:"summary",title:"信息确认",component:function({data:e}){let[s,a]=(0,r.useState)(!1),[i,n]=(0,r.useState)(!1),d=m.Fr.find(s=>s.code===e.mbtiType),c=[...e.personalityTags||[],...e.customTags||[]],x=e.radarData?(Object.values(e.radarData).reduce((e,s)=>e+s,0)/6).toFixed(1):"0",h=async()=>{a(!0),await new Promise(e=>setTimeout(e,2e3)),a(!1),n(!0)};return i?(0,t.jsxs)("div",{className:"text-center space-y-6",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-[var(--mint-green)] rounded-full flex items-center justify-center mx-auto",children:(0,t.jsx)(v.A,{className:"w-10 h-10 text-white"})}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)]",children:"\uD83C\uDF89 合作画像创建成功！"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)] max-w-md mx-auto",children:"你的个人合作画像已经创建完成。现在可以查看为你推荐的合作伙伴和项目了！"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(o(),{href:"/profile",children:(0,t.jsx)(l.$,{size:"lg",children:"查看我的画像"})}),(0,t.jsx)(o(),{href:"/match",children:(0,t.jsx)(l.$,{variant:"outline",size:"lg",children:"查看匹配结果"})})]})]}):(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"信息确认"}),(0,t.jsx)("p",{className:"text-[var(--soft-gray)]",children:"请确认你的合作画像信息，确认无误后即可完成创建"})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(S.A,{className:"w-5 h-5 text-[var(--coral-pink)] mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)]",children:"基本信息"})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"姓名："}),e.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"年龄："}),e.age]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"性别："}),e.gender]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"地区："}),e.location]}),e.email&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"邮箱："}),e.email]})]})]}),(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(C,{className:"w-5 h-5 text-[var(--mint-green)] mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)]",children:"技能资源"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[e.skills&&e.skills.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{className:"text-sm",children:"技能："}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:e.skills.map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded",children:e},s))})]}),e.funding&&(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("strong",{children:"资金情况："}),e.funding]})]})]}),d&&(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(p,{className:"w-5 h-5 text-[var(--brand-purple)] mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)]",children:"MBTI类型"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-[var(--brand-purple)] mr-3",children:d.code}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:d.name}),(0,t.jsx)("div",{className:"text-sm text-[var(--soft-gray)]",children:d.description})]})]})]}),(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(j.A,{className:"w-5 h-5 text-[var(--coral-pink)] mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)]",children:"合作偏好"})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[e.teamSize&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"团队规模："}),e.teamSize]}),e.communicationStyle&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"沟通方式："}),e.communicationStyle]}),e.workStyle&&e.workStyle.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"工作方式："}),(0,t.jsx)("span",{className:"ml-1",children:e.workStyle.join(", ")})]})]})]}),(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(u.A,{className:"w-5 h-5 text-[var(--ice-blue)] mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)]",children:"时间投入"})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[e.timeAvailable&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"可投入时间："}),e.timeAvailable]}),e.commitment&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"承诺程度："}),e.commitment]}),e.timeline&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"期望时间线："}),e.timeline]})]})]}),c.length>0&&(0,t.jsxs)("div",{className:"bg-[var(--mist-white)] p-6 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(P,{className:"w-5 h-5 text-[var(--mint-green)] mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)]",children:"个性标签"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:c.map((e,s)=>(0,t.jsx)("span",{className:"px-3 py-1 bg-[var(--coral-pink)] text-white text-sm rounded-full",children:e},s))})]})]}),e.radarData&&(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg card-shadow",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(A,{className:"w-5 h-5 text-[var(--brand-purple)] mr-2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)]",children:"能力评估"})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:m.RW.map(s=>(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-[var(--brand-purple)]",children:e.radarData[s.key]}),(0,t.jsx)("div",{className:"text-sm text-[var(--dark-blue-gray)]",children:s.label})]},s.key))}),(0,t.jsxs)("div",{className:"text-center mt-4 pt-4 border-t",children:[(0,t.jsx)("div",{className:"text-sm text-[var(--soft-gray)]",children:"综合评分"}),(0,t.jsx)("div",{className:"text-3xl font-bold text-[var(--coral-pink)]",children:x})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-800 mb-2",children:"\uD83D\uDD12 隐私保护说明"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• 你的个人信息将被安全保护，不会泄露给第三方"}),(0,t.jsx)("li",{children:"• 你可以随时修改或删除你的合作画像"}),(0,t.jsx)("li",{children:"• 匹配过程中只会显示必要的合作相关信息"}),(0,t.jsx)("li",{children:"• 联系方式只有在双方同意后才会共享"})]})]}),(0,t.jsx)("div",{className:"flex justify-center mt-8",children:(0,t.jsx)(l.$,{onClick:h,size:"lg",disabled:s,className:"px-8",children:s?"创建中...":"确认创建合作画像"})})]})}}];function D(){let[e,s]=(0,r.useState)(0),[a,n]=(0,r.useState)({}),c=()=>{e<$.length-1&&s(e+1)},m=()=>{e>0&&s(e-1)},x=$[e].component;return(0,t.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(o(),{href:"/",className:"flex items-center text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]",children:[(0,t.jsx)(i.A,{className:"w-5 h-5 mr-2"}),"返回首页"]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-xl font-semibold text-[var(--dark-blue-gray)]",children:"创建你的合作画像"}),(0,t.jsxs)("p",{className:"text-sm text-[var(--soft-gray)]",children:["步骤 ",e+1," / ",$.length]})]}),(0,t.jsx)("div",{className:"w-20"})," "]})})}),(0,t.jsx)("div",{className:"bg-white border-b",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"flex items-center py-4",children:$.map((s,a)=>(0,t.jsxs)("div",{className:"flex items-center flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${a<=e?"bg-[var(--coral-pink)] text-white":"bg-[var(--mist-white)] text-[var(--soft-gray)]"}`,children:a+1}),(0,t.jsx)("span",{className:`ml-2 text-sm font-medium ${a<=e?"text-[var(--dark-blue-gray)]":"text-[var(--soft-gray)]"}`,children:s.title})]}),a<$.length-1&&(0,t.jsx)("div",{className:`flex-1 h-1 mx-4 rounded ${a<e?"bg-[var(--coral-pink)]":"bg-[var(--mist-white)]"}`})]},s.id))})})}),(0,t.jsxs)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsx)("div",{className:"bg-white rounded-xl card-shadow p-8",children:(0,t.jsx)(x,{data:a,onDataChange:e=>{n(s=>({...s,...e}))},onNext:c,onPrev:m})}),(0,t.jsxs)("div",{className:"flex justify-between mt-8",children:[(0,t.jsxs)(l.$,{variant:"outline",onClick:m,disabled:0===e,className:"flex items-center",children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"上一步"]}),(0,t.jsxs)(l.$,{onClick:c,disabled:e===$.length-1,className:"flex items-center",children:["下一步",(0,t.jsx)(d,{className:"w-4 h-4 ml-2"})]})]})]})]})}},49699:(e,s,a)=>{"use strict";a.d(s,{$:()=>l});var t=a(60687),r=a(49384);let l=(0,a(43210).forwardRef)(({className:e,variant:s="primary",size:a="md",...l},i)=>(0,t.jsx)("button",{className:function(...e){return(0,r.$)(e)}("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{"px-3 py-1.5 text-sm":"sm"===a,"px-4 py-2 text-base":"md"===a,"px-6 py-3 text-lg":"lg"===a},{"bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)]":"primary"===s,"bg-[var(--mint-green)] text-white hover:bg-[#16a085] focus:ring-[var(--mint-green)]":"secondary"===s,"bg-[var(--brand-purple)] text-white hover:bg-[#7d3c98] focus:ring-[var(--brand-purple)]":"accent"===s,"border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]":"outline"===s,"text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)] focus:ring-[var(--soft-gray)]":"ghost"===s},e),ref:i,...l}));l.displayName="Button"},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63139:(e,s,a)=>{Promise.resolve().then(a.bind(a,49445))},67760:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},69459:()=>{},70440:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});var t=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71616:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},79551:e=>{"use strict";e.exports=require("url")},87115:(e,s,a)=>{Promise.resolve().then(a.bind(a,93031))},90092:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=a(65239),r=a(48088),l=a(88170),i=a.n(l),n=a(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let c={children:["",{children:["experience",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,93031)),"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/experience/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/experience/page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/experience/page",pathname:"/experience",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},93031:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/experience/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/experience/page.tsx","default")},94431:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>c,metadata:()=>d});var t=a(37413),r=a(2202),l=a.n(r),i=a(64988),n=a.n(i);a(61135);let d={title:"Create Next App",description:"Generated by create next app"};function c({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsx)("body",{className:`${l().variable} ${n().variable} antialiased`,children:e})})}}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[447,169,167,747],()=>a(90092));module.exports=t})();