(()=>{var e={};e.id=684,e.ids=[684],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8751:(e,s,t)=>{"use strict";t.d(s,{Fr:()=>r,RW:()=>i,ku:()=>n,o1:()=>l,uf:()=>a});let a={skills:["前端开发","后端开发","产品设计","用户体验","数据分析","市场营销","商业策划","项目管理","内容创作","视觉设计"],personality:["团队协作","独立思考","创新思维","执行力强","沟通能力强","学习能力强","抗压能力强","领导力","细心负责","目标导向"],workStyle:["远程工作","线下协作","灵活时间","规律作息","快速迭代","深度思考","数据驱动","用户导向","技术驱动","商业导向"],interests:["人工智能","区块链","电商平台","教育科技","健康医疗","金融科技","社交网络","游戏娱乐","企业服务","生活服务"]},r=[{code:"INTJ",name:"建筑师",description:"富有想象力和战略性的思想家"},{code:"INTP",name:"思想家",description:"具有创造性的发明家"},{code:"ENTJ",name:"指挥官",description:"大胆、富有想象力、意志强烈的领导者"},{code:"ENTP",name:"辩论家",description:"聪明好奇的思想家"},{code:"INFJ",name:"提倡者",description:"安静而神秘的理想主义者"},{code:"INFP",name:"调停者",description:"诗意、善良的利他主义者"},{code:"ENFJ",name:"主人公",description:"富有魅力、鼓舞人心的领导者"},{code:"ENFP",name:"竞选者",description:"热情、有创造力的自由精神"},{code:"ISTJ",name:"物流师",description:"实用主义的事实导向者"},{code:"ISFJ",name:"守护者",description:"非常专注、温暖的守护者"},{code:"ESTJ",name:"总经理",description:"出色的管理者"},{code:"ESFJ",name:"执政官",description:"极有同情心、受欢迎的人"},{code:"ISTP",name:"鉴赏家",description:"大胆而实际的实验者"},{code:"ISFP",name:"探险家",description:"灵活、有魅力的艺术家"},{code:"ESTP",name:"企业家",description:"聪明、精力充沛的感知者"},{code:"ESFP",name:"娱乐家",description:"自发的、精力充沛的娱乐者"}],i=[{key:"communication",label:"沟通力",description:"表达和理解他人的能力"},{key:"execution",label:"执行力",description:"将想法转化为行动的能力"},{key:"empathy",label:"共情力",description:"理解和感受他人情感的能力"},{key:"analysis",label:"分析力",description:"逻辑思维和问题解决能力"},{key:"learning",label:"学习力",description:"快速掌握新知识和技能的能力"},{key:"leadership",label:"领导力",description:"影响和激励他人的能力"}],l=[{id:"1",name:"张小明",age:28,location:"北京",mbti:"INTJ",skills:["前端开发","产品设计"],tags:["团队协作","创新思维","远程工作"],radarData:{communication:8,execution:9,empathy:6,analysis:9,learning:8,leadership:7},compatibility:92},{id:"2",name:"李小红",age:25,location:"上海",mbti:"ENFP",skills:["市场营销","内容创作"],tags:["沟通能力强","创新思维","用户导向"],radarData:{communication:9,execution:7,empathy:9,analysis:6,learning:8,leadership:8},compatibility:87},{id:"3",name:"王小华",age:30,location:"深圳",mbti:"ESTJ",skills:["项目管理","商业策划"],tags:["执行力强","领导力","目标导向"],radarData:{communication:8,execution:9,empathy:7,analysis:8,learning:7,leadership:9},compatibility:85}],n=[{id:"1",title:"AI驱动的学习平台",description:"基于人工智能的个性化学习推荐系统",tags:["人工智能","教育科技","前端开发"],teamSize:4,progress:30,compatibility:94},{id:"2",title:"可持续生活社区",description:"连接环保爱好者的社交平台",tags:["社交网络","生活服务","用户体验"],teamSize:3,progress:15,compatibility:89}]},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21730:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(60687),r=t(43210),i=t(49699),l=t(8751),n=t(62688);let d=(0,n.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var c=t(41312),o=t(28947),m=t(58869);let x=(0,n.A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);var p=t(67760),h=t(48730);let v=(0,n.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var u=t(85814),b=t.n(u);function g(){let[e,s]=(0,r.useState)("people"),[t,n]=(0,r.useState)(!1),u=l.o1.slice(1),g=l.ku;return(0,a.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(b(),{href:"/",className:"text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]",children:"← 返回首页"}),(0,a.jsx)("h1",{className:"text-xl font-semibold text-[var(--dark-blue-gray)]",children:"智能匹配推荐"}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>n(!t),children:[(0,a.jsx)(d,{className:"w-4 h-4 mr-2"}),"筛选"]})]})})}),(0,a.jsxs)("main",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-center mb-8",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-1 shadow-sm",children:[(0,a.jsxs)("button",{onClick:()=>s("people"),className:`px-6 py-2 rounded-md font-medium transition-all ${"people"===e?"bg-[var(--coral-pink)] text-white":"text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)]"}`,children:[(0,a.jsx)(c.A,{className:"w-4 h-4 inline mr-2"}),"合作伙伴"]}),(0,a.jsxs)("button",{onClick:()=>s("projects"),className:`px-6 py-2 rounded-md font-medium transition-all ${"projects"===e?"bg-[var(--coral-pink)] text-white":"text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)]"}`,children:[(0,a.jsx)(o.A,{className:"w-4 h-4 inline mr-2"}),"推荐项目"]})]})}),"people"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"为你推荐的合作伙伴"}),(0,a.jsx)("p",{className:"text-[var(--soft-gray)]",children:"基于你的技能、性格和偏好，我们为你找到了这些潜在的合作伙伴"})]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:u.map(e=>{let s=l.Fr.find(s=>s.code===e.mbti);return(0,a.jsxs)("div",{className:"bg-white rounded-xl card-shadow card-hover p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)(m.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-[var(--dark-blue-gray)]",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-[var(--soft-gray)]",children:[e.age,"岁 \xb7 ",e.location]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-[var(--mint-green)]",children:[e.compatibility,"%"]}),(0,a.jsx)("div",{className:"text-xs text-[var(--soft-gray)]",children:"匹配度"})]})]}),s&&(0,a.jsx)("div",{className:"bg-[var(--mist-white)] p-3 rounded-lg mb-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"font-bold text-[var(--brand-purple)] mr-2",children:s.code}),(0,a.jsx)("span",{className:"text-sm text-[var(--dark-blue-gray)]",children:s.name})]})}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"核心技能"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.skills.map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 bg-[var(--coral-pink)] text-white text-xs rounded",children:e},s))})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-[var(--dark-blue-gray)] mb-2",children:"个性特质"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e,s)=>(0,a.jsx)("span",{className:"px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded",children:e},s))})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg mb-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-blue-800 mb-1",children:"匹配原因"}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"技能互补度高 \xb7 性格类型匹配 \xb7 工作方式相似"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.$,{size:"sm",className:"flex-1",children:[(0,a.jsx)(x,{className:"w-4 h-4 mr-1"}),"发起聊天"]}),(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:(0,a.jsx)(p.A,{className:"w-4 h-4"})})]})]},e.id)})})]}),"projects"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-[var(--dark-blue-gray)] mb-2",children:"推荐项目"}),(0,a.jsx)("p",{className:"text-[var(--soft-gray)]",children:"根据你的技能和兴趣，这些项目可能很适合你参与"})]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:g.map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-xl card-shadow card-hover p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-[var(--soft-gray)] text-sm mb-3",children:e.description})]}),(0,a.jsxs)("div",{className:"text-right ml-4",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-[var(--mint-green)]",children:[e.compatibility,"%"]}),(0,a.jsx)("div",{className:"text-xs text-[var(--soft-gray)]",children:"匹配度"})]})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map((e,s)=>(0,a.jsx)("span",{className:"px-3 py-1 bg-[var(--ice-blue)] text-white text-sm rounded-full",children:e},s))})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 text-[var(--soft-gray)] mr-2"}),(0,a.jsxs)("span",{className:"text-sm",children:["团队 ",e.teamSize," 人"]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 text-[var(--soft-gray)] mr-2"}),(0,a.jsxs)("span",{className:"text-sm",children:["进度 ",e.progress,"%"]})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{className:"text-[var(--dark-blue-gray)]",children:"项目进度"}),(0,a.jsxs)("span",{className:"text-[var(--soft-gray)]",children:[e.progress,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-[var(--mist-white)] rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-[var(--mint-green)] h-2 rounded-full transition-all",style:{width:`${e.progress}%`}})})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg mb-4",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-green-800 mb-1",children:"推荐原因"}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"技能需求匹配 \xb7 项目类型符合兴趣 \xb7 团队规模合适"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{size:"sm",className:"flex-1",children:"申请加入"}),(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:(0,a.jsx)(v,{className:"w-4 h-4"})})]})]},e.id))})]}),(0,a.jsx)("div",{className:"mt-12 text-center",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 card-shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-[var(--dark-blue-gray)] mb-2",children:"没有找到合适的匹配？"}),(0,a.jsx)("p",{className:"text-[var(--soft-gray)] mb-4",children:"我们会持续为你寻找新的合作机会，也可以完善你的画像信息来获得更精准的推荐"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,a.jsx)(b(),{href:"/profile",children:(0,a.jsx)(i.$,{variant:"outline",children:"完善画像信息"})}),(0,a.jsx)(i.$,{children:"设置推荐提醒"})]})]})})]})]})}},28947:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30580:(e,s,t)=>{Promise.resolve().then(t.bind(t,98978))},32667:()=>{},33873:e=>{"use strict";e.exports=require("path")},35168:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},41040:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=t(65239),r=t(48088),i=t(88170),l=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["match",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,98978)),"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/match/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/match/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/match/page",pathname:"/match",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},41312:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49140:(e,s,t)=>{Promise.resolve().then(t.bind(t,21730))},49699:(e,s,t)=>{"use strict";t.d(s,{$:()=>i});var a=t(60687),r=t(49384);let i=(0,t(43210).forwardRef)(({className:e,variant:s="primary",size:t="md",...i},l)=>(0,a.jsx)("button",{className:function(...e){return(0,r.$)(e)}("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{"px-3 py-1.5 text-sm":"sm"===t,"px-4 py-2 text-base":"md"===t,"px-6 py-3 text-lg":"lg"===t},{"bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)]":"primary"===s,"bg-[var(--mint-green)] text-white hover:bg-[#16a085] focus:ring-[var(--mint-green)]":"secondary"===s,"bg-[var(--brand-purple)] text-white hover:bg-[#7d3c98] focus:ring-[var(--brand-purple)]":"accent"===s,"border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]":"outline"===s,"text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)] focus:ring-[var(--soft-gray)]":"ghost"===s},e),ref:l,...i}));i.displayName="Button"},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67760:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},69459:()=>{},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71616:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},79551:e=>{"use strict";e.exports=require("url")},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,metadata:()=>d});var a=t(37413),r=t(2202),i=t.n(r),l=t(64988),n=t.n(l);t(61135);let d={title:"Create Next App",description:"Generated by create next app"};function c({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:`${i().variable} ${n().variable} antialiased`,children:e})})}},98978:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/match/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/vscode/线上协作与创业撮合平台/collaboration-platform/src/app/match/page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,169,167],()=>t(41040));module.exports=a})();