<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/569ce4b8f30dc480-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/93f479601ee12b01-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/9959d14f85dbef39.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ad967276a8926d54.js"/><script src="/_next/static/chunks/4bd1b696-a06c382ed846a660.js" async=""></script><script src="/_next/static/chunks/303-30c1863249c8355b.js" async=""></script><script src="/_next/static/chunks/main-app-7c2357ce8f18aebe.js" async=""></script><script src="/_next/static/chunks/178-65bdbde0b09b5f77.js" async=""></script><script src="/_next/static/chunks/app/match/page-85f6013b015739fd.js" async=""></script><meta name="next-size-adjust" content=""/><title>Create Next App</title><meta name="description" content="Generated by create next app"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div class="min-h-screen gradient-bg"><header class="bg-white shadow-sm"><div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><div class="flex items-center justify-between"><a class="text-[var(--coral-pink)] hover:text-[var(--dark-blue-gray)]" href="/">← 返回首页</a><h1 class="text-xl font-semibold text-[var(--dark-blue-gray)]">智能匹配推荐</h1><button class="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-3 py-1.5 text-sm border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-funnel w-4 h-4 mr-2" aria-hidden="true"><path d="M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z"></path></svg>筛选</button></div></div></header><main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="flex items-center justify-center mb-8"><div class="bg-white rounded-lg p-1 shadow-sm"><button class="px-6 py-2 rounded-md font-medium transition-all bg-[var(--coral-pink)] text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-4 h-4 inline mr-2" aria-hidden="true"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><path d="M16 3.128a4 4 0 0 1 0 7.744"></path><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><circle cx="9" cy="7" r="4"></circle></svg>合作伙伴</button><button class="px-6 py-2 rounded-md font-medium transition-all text-[var(--dark-blue-gray)] hover:bg-[var(--mist-white)]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-target w-4 h-4 inline mr-2" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle></svg>推荐项目</button></div></div><div class="space-y-6"><div class="text-center mb-8"><h2 class="text-2xl font-bold text-[var(--dark-blue-gray)] mb-2">为你推荐的合作伙伴</h2><p class="text-[var(--soft-gray)]">基于你的技能、性格和偏好，我们为你找到了这些潜在的合作伙伴</p></div><div class="grid md:grid-cols-2 gap-6"><div class="bg-white rounded-xl card-shadow card-hover p-6"><div class="flex items-center justify-between mb-4"><div class="flex items-center"><div class="w-12 h-12 bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] rounded-full flex items-center justify-center mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user w-6 h-6 text-white" aria-hidden="true"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg></div><div><h3 class="font-semibold text-[var(--dark-blue-gray)]">李小红</h3><p class="text-sm text-[var(--soft-gray)]">25<!-- -->岁 · <!-- -->上海</p></div></div><div class="text-right"><div class="text-2xl font-bold text-[var(--mint-green)]">87<!-- -->%</div><div class="text-xs text-[var(--soft-gray)]">匹配度</div></div></div><div class="bg-[var(--mist-white)] p-3 rounded-lg mb-4"><div class="flex items-center"><span class="font-bold text-[var(--brand-purple)] mr-2">ENFP</span><span class="text-sm text-[var(--dark-blue-gray)]">竞选者</span></div></div><div class="mb-4"><div class="text-sm font-medium text-[var(--dark-blue-gray)] mb-2">核心技能</div><div class="flex flex-wrap gap-1"><span class="px-2 py-1 bg-[var(--coral-pink)] text-white text-xs rounded">市场营销</span><span class="px-2 py-1 bg-[var(--coral-pink)] text-white text-xs rounded">内容创作</span></div></div><div class="mb-4"><div class="text-sm font-medium text-[var(--dark-blue-gray)] mb-2">个性特质</div><div class="flex flex-wrap gap-1"><span class="px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded">沟通能力强</span><span class="px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded">创新思维</span><span class="px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded">用户导向</span></div></div><div class="bg-blue-50 p-3 rounded-lg mb-4"><div class="text-sm font-medium text-blue-800 mb-1">匹配原因</div><div class="text-xs text-blue-600">技能互补度高 · 性格类型匹配 · 工作方式相似</div></div><div class="flex gap-2"><button class="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-3 py-1.5 text-sm bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)] flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4 mr-1" aria-hidden="true"><path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg>发起聊天</button><button class="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-3 py-1.5 text-sm border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart w-4 h-4" aria-hidden="true"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg></button></div></div><div class="bg-white rounded-xl card-shadow card-hover p-6"><div class="flex items-center justify-between mb-4"><div class="flex items-center"><div class="w-12 h-12 bg-gradient-to-r from-[var(--coral-pink)] to-[var(--brand-purple)] rounded-full flex items-center justify-center mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user w-6 h-6 text-white" aria-hidden="true"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg></div><div><h3 class="font-semibold text-[var(--dark-blue-gray)]">王小华</h3><p class="text-sm text-[var(--soft-gray)]">30<!-- -->岁 · <!-- -->深圳</p></div></div><div class="text-right"><div class="text-2xl font-bold text-[var(--mint-green)]">85<!-- -->%</div><div class="text-xs text-[var(--soft-gray)]">匹配度</div></div></div><div class="bg-[var(--mist-white)] p-3 rounded-lg mb-4"><div class="flex items-center"><span class="font-bold text-[var(--brand-purple)] mr-2">ESTJ</span><span class="text-sm text-[var(--dark-blue-gray)]">总经理</span></div></div><div class="mb-4"><div class="text-sm font-medium text-[var(--dark-blue-gray)] mb-2">核心技能</div><div class="flex flex-wrap gap-1"><span class="px-2 py-1 bg-[var(--coral-pink)] text-white text-xs rounded">项目管理</span><span class="px-2 py-1 bg-[var(--coral-pink)] text-white text-xs rounded">商业策划</span></div></div><div class="mb-4"><div class="text-sm font-medium text-[var(--dark-blue-gray)] mb-2">个性特质</div><div class="flex flex-wrap gap-1"><span class="px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded">执行力强</span><span class="px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded">领导力</span><span class="px-2 py-1 bg-[var(--mint-green)] text-white text-xs rounded">目标导向</span></div></div><div class="bg-blue-50 p-3 rounded-lg mb-4"><div class="text-sm font-medium text-blue-800 mb-1">匹配原因</div><div class="text-xs text-blue-600">技能互补度高 · 性格类型匹配 · 工作方式相似</div></div><div class="flex gap-2"><button class="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-3 py-1.5 text-sm bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)] flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4 mr-1" aria-hidden="true"><path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path></svg>发起聊天</button><button class="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-3 py-1.5 text-sm border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart w-4 h-4" aria-hidden="true"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg></button></div></div></div></div><div class="mt-12 text-center"><div class="bg-white rounded-lg p-6 card-shadow"><h3 class="text-lg font-semibold text-[var(--dark-blue-gray)] mb-2">没有找到合适的匹配？</h3><p class="text-[var(--soft-gray)] mb-4">我们会持续为你寻找新的合作机会，也可以完善你的画像信息来获得更精准的推荐</p><div class="flex flex-col sm:flex-row gap-3 justify-center"><a href="/profile"><button class="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2 text-base border-2 border-[var(--coral-pink)] text-[var(--coral-pink)] hover:bg-[var(--coral-pink)] hover:text-white focus:ring-[var(--coral-pink)]">完善画像信息</button></a><button class="inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2 text-base bg-[var(--coral-pink)] text-white hover:bg-[#ff5252] focus:ring-[var(--coral-pink)]">设置推荐提醒</button></div></div></div></main></div><!--$--><!--/$--><!--$--><!--/$--><script src="/_next/static/chunks/webpack-ad967276a8926d54.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[87555,[],\"\"]\n3:I[31295,[],\"\"]\n4:I[90894,[],\"ClientPageRoot\"]\n5:I[17618,[\"178\",\"static/chunks/178-65bdbde0b09b5f77.js\",\"684\",\"static/chunks/app/match/page-85f6013b015739fd.js\"],\"default\"]\n8:I[59665,[],\"MetadataBoundary\"]\na:I[59665,[],\"OutletBoundary\"]\nd:I[74911,[],\"AsyncMetadataOutlet\"]\nf:I[59665,[],\"ViewportBoundary\"]\n11:I[26614,[],\"\"]\n:HL[\"/_next/static/media/569ce4b8f30dc480-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/93f479601ee12b01-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/9959d14f85dbef39.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"lsj633YmXqIjOh0wWFUgA\",\"p\":\"\",\"c\":[\"\",\"match\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"match\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/9959d14f85dbef39.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"match\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"R3mw8SvYqsQlimnVw-cXn\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[74911,[],\"AsyncMetadata\"]\n6:{}\n7:{}\n9:[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Create Next App\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Generated by create next app\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\ne:{\"metadata\":\"$14:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>